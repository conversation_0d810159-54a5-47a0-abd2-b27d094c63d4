import React, { useEffect, useImperativeHandle, useMemo, useRef, forwardRef, useState } from "react";
import { Platform, StyleProp, ViewStyle, View } from "react-native";
import { WebView, WebViewMessageEvent } from "react-native-webview";
import { Asset } from "expo-asset";
import TextOverlay from "./TextOverlay";
import { OCRPage } from "@/utils/ocr";

export type PDFViewerSource = {
  kind: "pdf" | "image";
  data: string; // pdf: base64 (no prefix). image: full data URL or file URI
  name?: string;
};

export type PDFViewerProps = {
  source: PDFViewerSource;
  initialZoom?: number;
  rotation?: number; // degrees
  style?: StyleProp<ViewStyle>;
  onReady?: () => void;
  onError?: (message: string) => void;
  onMetrics?: (m: { pageCount?: number; currentPage?: number; scale?: number; rotation?: number }) => void;
  // Text overlay props
  textOverlayData?: OCRPage[];
  showTextOverlay?: boolean;
  onTextOverlayToggle?: () => void;
};

export type PDFViewerRef = {
  zoomIn: () => void;
  zoomOut: () => void;
  rotate: (deg: number) => void;
  fitWidth: () => void;
};

const baseHtml = (
  zoom: number,
  rotation: number,
  pdfJsUri: string,
  workerUri: string,
) => `<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
    <title>PDF.js Viewer</title>
    <style>
      html, body { margin: 0; padding: 0; background:#f5f5f5; }
      #toolbar { position: sticky; top: 0; background: #fff; border-bottom: 1px solid #e5e7eb; padding: 8px; display: none; gap: 8px; align-items:center; z-index: 10; }
      #status { margin-left: auto; font: 12px system-ui; color: #6B7280; }
      #viewer { padding: 10px; display: flex; flex-direction: column; align-items: center; gap: 10px; }
      canvas { background: white; box-shadow: 0 2px 8px rgba(0,0,0,0.08); border-radius: 4px; }
      .hidden { display: none; }
      button { border: 1px solid #e5e7eb; background: #fff; border-radius: 6px; padding: 6px 10px; font: 12px system-ui; cursor: pointer; }
    </style>
    <script src="${pdfJsUri}"></script>
  </head>
  <body>
    <div id="toolbar">
      <button id="zoomOut">-</button>
      <button id="zoomIn">+</button>
      <button id="fitWidth">Fit</button>
      <span id="status">Loading…</span>
    </div>
    <div id="viewer"></div>
    <script>
      // Immediate JavaScript execution test
      console.log('[WebView JS] JavaScript execution started');
      console.log('[WebView JS] Window object available:', typeof window);
      console.log('[WebView JS] ReactNativeWebView available:', !!window.ReactNativeWebView);

      const isRN = !!window.ReactNativeWebView;
      const send = m => {
        console.log('[WebView JS] Attempting to send message:', m);
        if (isRN) {
          try {
            window.ReactNativeWebView.postMessage(JSON.stringify(m));
            console.log('[WebView JS] Message sent successfully');
          } catch (e) {
            console.log('[WebView JS] Message send failed:', e);
          }
        } else {
          console.log('[WebView JS] ReactNativeWebView not available, cannot send message');
        }
      };

      // Test communication immediately
      send({ type: 'js-test', payload: 'JavaScript execution and communication working' });

      const state = { pdf: null, pageCount: 0, scale: ${zoom.toFixed(2)}, rotation: ${rotation|0} };

      // Configure worker to vendored asset URI.
      console.log('[WebView JS] Checking for pdfjsLib...');
      if (window['pdfjsLib']) {
        console.log('[WebView JS] pdfjsLib found, configuring worker...');
        pdfjsLib.GlobalWorkerOptions.workerSrc = '${workerUri}';
        console.log('[WebView JS] Worker configured to:', pdfjsLib.GlobalWorkerOptions.workerSrc);
      } else {
        console.log('[WebView JS] pdfjsLib NOT found - this is the problem!');
      }

      const viewer = document.getElementById('viewer');
      const statusEl = document.getElementById('status');
      const btnIn = document.getElementById('zoomIn');
      const btnOut = document.getElementById('zoomOut');
      const btnFit = document.getElementById('fitWidth');

      function setStatus(t){ statusEl.textContent = t; }

      let io = null; // IntersectionObserver
      const rendered = new Set();
      const pageMetrics = new Map(); // Store page metrics (size + offsets)

      function detachIO(){ if (io) { try { io.disconnect(); } catch(e){} io = null; } }

      function renderPageInto(el, num){
        if (rendered.has(num)) return;
        rendered.add(num);
        state.pdf.getPage(num).then(function(page){
          const viewport = page.getViewport({ scale: state.scale, rotation: state.rotation });
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');
          canvas.width = viewport.width;
          canvas.height = viewport.height;
          canvas.style.maxWidth = '100%';
          canvas.style.height = 'auto';
          el.innerHTML = '';
          el.appendChild(canvas);
          const renderContext = { canvasContext: context, viewport: viewport };
          page.render(renderContext);
        });
      }

      function setupPlaceholders(){
        detachScrollTracking();
        viewer.innerHTML = '';
        rendered.clear();
        detachIO();
        io = new IntersectionObserver((entries) => {
          for (const entry of entries){
            if (entry.isIntersecting){
              const target = entry.target;
              const num = parseInt(target.getAttribute('data-num'));
              renderPageInto(target, num);
            }
          }
        }, { root: null, rootMargin: '200px 0px', threshold: 0.01 });

        // Use page 1 to estimate size for placeholders
        state.pdf.getPage(1).then(function(page){
          const vp = page.getViewport({ scale: state.scale, rotation: state.rotation });
          for (let i=1;i<=state.pageCount;i++){
            const holder = document.createElement('div');
            holder.className = 'page-holder';
            holder.setAttribute('data-num', String(i));
            holder.style.width = vp.width + 'px';
            holder.style.height = vp.height + 'px';
            holder.style.maxWidth = '100%';
            holder.style.margin = '0 auto';
            holder.style.background = '#fff';
            holder.style.boxShadow = '0 2px 8px rgba(0,0,0,0.08)';
            holder.style.borderRadius = '4px';
            viewer.appendChild(holder);
            io.observe(holder);
          }
          // Send initial metrics after setup
          sendMetrics();
          setupScrollTracking();
        });
      }

      function sendMetrics(){
        const metrics = {
          scale: state.scale,
          rotation: state.rotation,
          pageCount: state.pageCount,
          currentPage: 1, // Default to first page
          pageMetrics: {}
        };

        // Collect metrics for each page holder
        const holders = document.querySelectorAll('.page-holder');
        let maxVisibleArea = 0;

        holders.forEach((holder, index) => {
          const rect = holder.getBoundingClientRect();
          const pageNum = index + 1;

          // Get the canvas element for natural dimensions
          const canvas = holder.querySelector('canvas');
          let naturalWidth = 0;
          let naturalHeight = 0;

          if (canvas) {
            // Get natural page dimensions from canvas
            naturalWidth = canvas.width;
            naturalHeight = canvas.height;
          }

          // Calculate visible area for current page detection
          const visibleLeft = Math.max(0, rect.left);
          const visibleTop = Math.max(0, rect.top);
          const visibleRight = Math.min(window.innerWidth, rect.right);
          const visibleBottom = Math.min(window.innerHeight, rect.bottom);
          const visibleWidth = Math.max(0, visibleRight - visibleLeft);
          const visibleHeight = Math.max(0, visibleBottom - visibleTop);
          const visibleArea = visibleWidth * visibleHeight;

          // Track the page with the most visible area (improved algorithm)
          const totalPageArea = rect.width * rect.height;
          const visibilityRatio = totalPageArea > 0 ? visibleArea / totalPageArea : 0;

          if (visibleArea > maxVisibleArea && visibilityRatio > 0.3) {
            maxVisibleArea = visibleArea;
            metrics.currentPage = pageNum;
          }

          // Enhanced page metrics with natural and rendered dimensions
          metrics.pageMetrics[pageNum] = {
            // Rendered dimensions (after scaling)
            width: rect.width,
            height: rect.height,
            offsetX: rect.left,
            offsetY: rect.top,

            // Natural page dimensions (unscaled)
            naturalWidth: naturalWidth,
            naturalHeight: naturalHeight,

            // Viewport information
            viewportWidth: window.innerWidth,
            viewportHeight: window.innerHeight,

            // Visibility information
            visibleArea: visibleArea,
            visibilityRatio: visibilityRatio,

            // Page rotation state
            rotation: state.rotation,

            // Scale factor
            scale: state.scale
          };
        });

        send({ type: 'metrics', payload: metrics });
      }

      function rerender(){
        setupPlaceholders();
        sendMetrics();
      }

      // Track current visible page and send metrics on scroll with throttling
      let scrollTimeout = null;
      let lastScrollTime = 0;
      const SCROLL_THROTTLE_MS = 150; // Increased throttle time for better performance

      function handleScroll(){
        const now = Date.now();

        // Clear existing timeout
        if (scrollTimeout) clearTimeout(scrollTimeout);

        // Throttle frequent scroll events
        if (now - lastScrollTime < SCROLL_THROTTLE_MS) {
          scrollTimeout = setTimeout(() => {
            sendMetrics();
            lastScrollTime = Date.now();
          }, SCROLL_THROTTLE_MS);
        } else {
          // Send immediately if enough time has passed
          sendMetrics();
          lastScrollTime = now;
        }
      }

      // Scroll tracking management
      let trackingAttached = false;
      function setupScrollTracking(){
        if (trackingAttached) return;
        window.addEventListener('scroll', handleScroll, { passive: true });
        viewer.addEventListener('scroll', handleScroll, { passive: true });
        trackingAttached = true;
      }
      function detachScrollTracking(){
        if (!trackingAttached) return;
        window.removeEventListener('scroll', handleScroll);
        viewer.removeEventListener('scroll', handleScroll);
        trackingAttached = false;
      }

      function fitWidth(){
        // crude fit to screen width
        const width = Math.min(window.innerWidth - 20, 1000);
        // Recompute scale against first page width at scale 1
        state.pdf.getPage(1).then(function(page){
          const vp = page.getViewport({ scale: 1, rotation: state.rotation });
          state.scale = width / vp.width;
          rerender();
        });
      }

      btnIn.onclick = () => { state.scale = Math.min(state.scale + 0.25, 5); rerender(); sendMetrics(); };
      btnOut.onclick = () => { state.scale = Math.max(state.scale - 0.25, 0.25); rerender(); sendMetrics(); };
      btnFit.onclick = () => { fitWidth(); sendMetrics(); };

      function handleMessage(e){
        try {
          const msg = JSON.parse(e.data);
          console.log('[PDFViewer WebView] Received message:', msg.type, msg.payload ? 'with payload' : 'no payload');

          if (msg.type === 'load-pdf'){
            const base64 = msg.payload; // no data: prefix
            console.log('[PDFViewer WebView] Loading PDF, base64 length:', base64.length);
            console.log('[PDFViewer WebView] PDF.js available:', typeof pdfjsLib !== 'undefined');
            console.log('[PDFViewer WebView] Worker configured:', pdfjsLib.GlobalWorkerOptions.workerSrc);

            const bytes = Uint8Array.from(atob(base64), c => c.charCodeAt(0));
            console.log('[PDFViewer WebView] Converted to bytes, length:', bytes.length);

            pdfjsLib.getDocument({ data: bytes }).promise.then(pdf => {
              console.log('[PDFViewer WebView] PDF loaded successfully, pages:', pdf.numPages);
              detachScrollTracking(); // Clean up before loading new document
              state.pdf = pdf;
              state.pageCount = pdf.numPages;
              setStatus(state.pageCount + ' pages');
              send({ type: 'ready', payload: { pageCount: state.pageCount } });
              setupPlaceholders();
            }).catch(err => {
              console.error('[PDFViewer WebView] PDF load error:', err);
              setStatus('Load error');
              send({ type: 'error', payload: String(err && err.message || err) });
            });
          } else if (msg.type === 'zoom-in'){ state.scale = Math.min(state.scale + 0.25, 5); rerender(); }
          else if (msg.type === 'zoom-out'){ state.scale = Math.max(state.scale - 0.25, 0.25); rerender(); }
          else if (msg.type === 'fit-width'){ fitWidth(); }
          else if (msg.type === 'rotate'){ state.rotation = ((state.rotation || 0) + (msg.payload|0)) % 360; rerender(); }
        } catch (e) {
          console.error('[PDFViewer WebView] Message handling error:', e);
        }
      }

      window.addEventListener('message', handleMessage);
      document.addEventListener('message', (e) => handleMessage({ data: e.data }));
      setStatus('Waiting for data…');
    </script>
  </body>
</html>`;

// Helper function to check if page metrics have meaningfully changed
function hasMetricsChanged(existing: any, updated: any): boolean {
  const epsilon = 0.1; // Small threshold for floating point comparisons

  return (
    Math.abs((existing.width || 0) - (updated.width || 0)) > epsilon ||
    Math.abs((existing.height || 0) - (updated.height || 0)) > epsilon ||
    Math.abs((existing.offsetX || 0) - (updated.offsetX || 0)) > epsilon ||
    Math.abs((existing.offsetY || 0) - (updated.offsetY || 0)) > epsilon ||
    (existing.rotation || 0) !== (updated.rotation || 0)
  );
}

export const PDFViewer = forwardRef<PDFViewerRef, PDFViewerProps>(function PDFViewer(
  {
    source,
    initialZoom = 1,
    rotation = 0,
    style,
    onReady,
    onError,
    onMetrics,
    textOverlayData,
    showTextOverlay = false,
    onTextOverlayToggle
  },
  ref,
) {
  const webRef = useRef<WebView>(null);
  const [html, setHtml] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [currentZoom, setCurrentZoom] = useState(initialZoom);
  const [currentRotation, setCurrentRotation] = useState(rotation);
  const [pageMetrics, setPageMetrics] = useState<Map<number, any>>(new Map());
  const [containerLayout, setContainerLayout] = useState<{ x: number; y: number; width: number; height: number } | null>(null);

  // Resolve vendored asset URIs for pdf.js and worker, then build HTML
  useEffect(() => {
    let mounted = true;
    async function prepare() {
      try {
        console.log('[PDFViewer] Starting PDF.js asset preparation...');
        const pdfAsset = Asset.fromModule(require("@/assets/pdfjs/pdf.min.pdfjs"));
        const workerAsset = Asset.fromModule(require("@/assets/pdfjs/pdf.worker.min.pdfjs"));

        console.log('[PDFViewer] PDF Asset initial state:', {
          uri: pdfAsset.uri,
          localUri: pdfAsset.localUri,
          downloaded: !!pdfAsset.localUri
        });
        console.log('[PDFViewer] Worker Asset initial state:', {
          uri: workerAsset.uri,
          localUri: workerAsset.localUri,
          downloaded: !!workerAsset.localUri
        });

        if (!pdfAsset.localUri) {
          console.log('[PDFViewer] Downloading PDF asset...');
          await pdfAsset.downloadAsync();
          console.log('[PDFViewer] PDF asset downloaded:', pdfAsset.localUri);
        }
        if (!workerAsset.localUri) {
          console.log('[PDFViewer] Downloading Worker asset...');
          await workerAsset.downloadAsync();
          console.log('[PDFViewer] Worker asset downloaded:', workerAsset.localUri);
        }

        const pdfUri = pdfAsset.localUri || pdfAsset.uri;
        const workerUri = workerAsset.localUri || workerAsset.uri;

        console.log('[PDFViewer] Final asset URIs:', { pdfUri, workerUri });

        if (mounted) {
          const htmlContent = baseHtml(initialZoom, rotation, pdfUri, workerUri);
          console.log('[PDFViewer] Generated HTML content length:', htmlContent.length);
          console.log('[PDFViewer] HTML content preview (first 500 chars):', htmlContent.substring(0, 500));

          // Add fallback test HTML for debugging WebView functionality
          const testHtml = `<!DOCTYPE html>
<html><head><title>WebView Test</title></head>
<body style="background: lightblue; padding: 20px; font-family: Arial;">
<h1>WebView Test</h1>
<p>If you can see this, the WebView is working!</p>
<p>PDF.js URIs: ${pdfUri ? 'PDF✓' : 'PDF✗'} ${workerUri ? 'Worker✓' : 'Worker✗'}</p>
<script>
console.log('[WebView Test] Test HTML loaded successfully');
window.ReactNativeWebView && window.ReactNativeWebView.postMessage(JSON.stringify({
  type: 'test',
  payload: 'WebView communication working'
}));
</script>
</body></html>`;

          // For debugging: temporarily use test HTML if PDF.js assets are missing
          const shouldUseTestHtml = !pdfUri || !workerUri;
          if (shouldUseTestHtml) {
            console.log('[PDFViewer] Using test HTML due to missing assets');
            setHtml(testHtml);
          } else {
            setHtml(htmlContent);
          }
          console.log('[PDFViewer] HTML content set in state');
        }
      } catch (e) {
        console.error('[PDFViewer] Asset preparation failed:', e);
        // If vendored assets fail for some reason, surface an error
        const fallbackHtml = baseHtml(initialZoom, rotation, "", "");
        console.log('[PDFViewer] Using fallback HTML, length:', fallbackHtml.length);
        setHtml(fallbackHtml);
        onError?.("Failed to load PDF.js assets");
      }
    }
    if (source.kind === 'pdf') {
      console.log('[PDFViewer] Source is PDF, preparing assets...');
      prepare();
    } else {
      console.log('[PDFViewer] Source is not PDF:', source.kind);
    }
    return () => { mounted = false; };
  }, [source.kind]); // Remove initialZoom and rotation to prevent infinite re-render loop

  useImperativeHandle(ref, () => ({
    zoomIn: () => webRef.current?.postMessage(JSON.stringify({ type: 'zoom-in' })),
    zoomOut: () => webRef.current?.postMessage(JSON.stringify({ type: 'zoom-out' })),
    fitWidth: () => webRef.current?.postMessage(JSON.stringify({ type: 'fit-width' })),
    rotate: (deg: number) => webRef.current?.postMessage(JSON.stringify({ type: 'rotate', payload: deg })),
  }), []);

  const handleMessage = (e: WebViewMessageEvent) => {
    try {
      const msg = JSON.parse(e.nativeEvent.data);

      // Add debug logging for Android (reduce spam for metrics messages)
      if (Platform.OS === 'android' && msg.type !== 'metrics') {
        console.log('[PDFViewer Android] Message received:', msg.type, msg.payload);
      }

      // Handle test messages for WebView functionality verification
      if (msg.type === 'test' || msg.type === 'js-test') {
        console.log('[PDFViewer] WebView test message received:', msg.type, msg.payload);
        return;
      }

      if (msg.type === 'ready') {
        onMetrics?.({ pageCount: msg.payload?.pageCount });
        onReady?.();
      } else if (msg.type === 'error') {
        const errorMsg = String(msg.payload ?? 'Unknown PDF error');
        console.error('[PDFViewer] Error:', errorMsg);
        onError?.(errorMsg);
      } else if (msg.type === 'zoom-in') {
        setCurrentZoom(prev => Math.min(prev + 0.25, 5));
      } else if (msg.type === 'zoom-out') {
        setCurrentZoom(prev => Math.max(prev - 0.25, 0.25));
      } else if (msg.type === 'rotate') {
        setCurrentRotation(prev => (prev + (msg.payload || 0)) % 360);
      } else if (msg.type === 'metrics') {
        const payload = msg.payload;
        const newPage = Math.max(0, (payload.currentPage || 1) - 1);
        const newZoom = payload.scale || currentZoom;
        const newRotation = payload.rotation || currentRotation;

        // Only update state if values actually changed to prevent unnecessary re-renders
        if (newPage !== currentPage) setCurrentPage(newPage);
        if (Math.abs(newZoom - currentZoom) > 0.01) setCurrentZoom(newZoom);
        if (newRotation !== currentRotation) setCurrentRotation(newRotation);

        // Update page metrics map with zero-based indexing and change detection
        const newMetrics = new Map(pageMetrics);
        let hasChanges = false;

        Object.entries(payload.pageMetrics || {}).forEach(([pageNum, metrics]: [string, any]) => {
          const pageIndex = parseInt(pageNum, 10) - 1;
          const existingMetrics = newMetrics.get(pageIndex);

          // Only update if metrics have meaningfully changed
          if (!existingMetrics || hasMetricsChanged(existingMetrics, metrics)) {
            newMetrics.set(pageIndex, metrics);
            hasChanges = true;
          }
        });

        if (hasChanges) {
          setPageMetrics(newMetrics);
        }

        // Notify parent component of metrics update
        onMetrics?.({
          pageCount: payload.pageCount,
          currentPage: payload.currentPage,
          scale: payload.scale,
          rotation: payload.rotation
        });
      }
    } catch {
      // ignore non-JSON logs
    }
  };

  // Load PDF data after WebView is ready
  useEffect(() => {
    if (html && source.kind === 'pdf' && webRef.current) {
      webRef.current.postMessage(JSON.stringify({ type: 'load-pdf', payload: source.data }));
    }
  }, [html, source]);

  // Image mode: render an HTML wrapper for consistent rotation/zoom behavior
  const imageHtml = useMemo(() => {
    const dataUrl = source.data.startsWith('data:') ? source.data : source.data;
    return `<!DOCTYPE html><html><head><meta name=viewport content="width=device-width,initial-scale=1"/><style>
      html,body{margin:0;padding:0;background:#f5f5f5}
      .wrap{display:flex;align-items:center;justify-content:center;min-height:100vh}
      img{max-width:100%;max-height:90vh;transform: scale(${initialZoom}) rotate(${rotation}deg);transition:transform .2s}
    </style></head><body><div class="wrap"><img src="${dataUrl}"/></div></body></html>`;
  }, [source, initialZoom, rotation]);

  // Debug WebView source (only log when source changes)
  const webViewSource = source.kind === 'image' ? { html: imageHtml } : (html ? { html } : undefined);
  const sourceInfo = {
    sourceKind: source.kind,
    hasImageHtml: !!imageHtml,
    hasHtml: !!html,
    htmlLength: html?.length || 0,
    webViewSourceDefined: !!webViewSource
  };

  // Only log when HTML content is first set or changes significantly
  if (html && html.length > 0 && !webViewSource._logged) {
    console.log('[PDFViewer] WebView source ready:', sourceInfo);
    (webViewSource as any)._logged = true;
  }

  const webViewContent = (
    <WebView
      ref={webRef}
      source={webViewSource}
      originWhitelist={["*"]}
      javaScriptEnabled
      domStorageEnabled
      allowFileAccess={source.kind === 'pdf'}
      onMessage={handleMessage}
      style={styles.webView}
      pointerEvents={showTextOverlay ? "none" : "auto"}
      // Android-specific optimizations for PDF rendering
      renderToHardwareTextureAndroid={true}
      androidLayerType="hardware"
      textZoom={100}
      scalesPageToFit={true}
      setBuiltInZoomControls={false}
      setDisplayZoomControls={false}
      nestedScrollEnabled={false}
      overScrollMode="never"
      cacheEnabled={true}
      // Additional Android WebView settings for better PDF compatibility
      mixedContentMode="compatibility"
      thirdPartyCookiesEnabled={false}
      sharedCookiesEnabled={false}
      geolocationEnabled={false}
      allowsProtectedMedia={false}
      // WebView lifecycle debugging
      onLoadStart={(syntheticEvent) => {
        const { nativeEvent } = syntheticEvent;
        console.log('[PDFViewer] WebView load started:', {
          url: nativeEvent.url,
          loading: nativeEvent.loading,
          title: nativeEvent.title
        });
      }}
      onLoad={(syntheticEvent) => {
        const { nativeEvent } = syntheticEvent;
        console.log('[PDFViewer] WebView loaded successfully:', {
          url: nativeEvent.url,
          loading: nativeEvent.loading,
          title: nativeEvent.title
        });
      }}
      onLoadEnd={(syntheticEvent) => {
        const { nativeEvent } = syntheticEvent;
        console.log('[PDFViewer] WebView load ended:', {
          url: nativeEvent.url,
          loading: nativeEvent.loading,
          title: nativeEvent.title
        });
      }}
      onLoadProgress={(syntheticEvent) => {
        const { nativeEvent } = syntheticEvent;
        console.log('[PDFViewer] WebView load progress:', {
          progress: nativeEvent.progress,
          url: nativeEvent.url
        });
      }}
      // Error handling
      onError={(syntheticEvent) => {
        const { nativeEvent } = syntheticEvent;
        console.error('[PDFViewer] WebView error:', nativeEvent);
        onError?.(`WebView error: ${nativeEvent.description || 'Unknown error'}`);
      }}
      onHttpError={(syntheticEvent) => {
        const { nativeEvent } = syntheticEvent;
        console.error('[PDFViewer] HTTP error:', nativeEvent.statusCode, nativeEvent.description);
        onError?.(`HTTP error: ${nativeEvent.statusCode} - ${nativeEvent.description}`);
      }}
      onRenderProcessGone={(syntheticEvent) => {
        const { nativeEvent } = syntheticEvent;
        console.error('[PDFViewer] Render process gone:', nativeEvent.didCrash);
        onError?.(`WebView render process ${nativeEvent.didCrash ? 'crashed' : 'was killed'}`);
      }}
    />
  );

  if (source.kind === 'image') {
    return (
      <View style={[styles.container, style]}>
        {webViewContent}
        <TextOverlay
          ocrPages={textOverlayData || []}
          currentPage={currentPage}
          zoom={currentZoom}
          rotation={currentRotation}
          visible={showTextOverlay}
          onToggleVisibility={onTextOverlayToggle}
          style={styles.textOverlay}
          pageMetrics={pageMetrics}
        />
      </View>
    );
  }

  return (
    <View
      style={[styles.container, style]}
      onLayout={(event) => {
        const { x, y, width, height } = event.nativeEvent.layout;
        setContainerLayout({ x, y, width, height });
      }}
    >
      {webViewContent}
      <TextOverlay
        ocrPages={textOverlayData || []}
        currentPage={currentPage}
        zoom={currentZoom}
        rotation={currentRotation}
        visible={showTextOverlay}
        onToggleVisibility={onTextOverlayToggle}
        style={styles.textOverlay}
        pageMetrics={pageMetrics}
        containerLayout={containerLayout}
      />
    </View>
  );
});

const styles = {
  container: {
    flex: 1,
    position: 'relative' as const,
  },
  webView: {
    flex: 1,
  },
  textOverlay: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
  },
};

export default PDFViewer;
