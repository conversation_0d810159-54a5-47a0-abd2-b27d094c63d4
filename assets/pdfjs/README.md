# PDF.js Assets (placeholder)

This folder is reserved for vendored PDF.js runtime files if/when we decide to avoid CDNs in the WebView. For now, the PDFViewer component loads PDF.js from a CDN for simplicity:

- https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js
- https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js

To vendor locally in a follow-up:
- Download the two files above into this directory but rename them to `pdf.min.pdfjs` and `pdf.worker.min.pdfjs`.
- We configure Metro to treat the `.pdfjs` extension as an asset (see `metro.config.js`) so React Native does not parse these scripts.
- The `PDFViewer` resolves their file URIs at runtime and loads them inside a WebView.
