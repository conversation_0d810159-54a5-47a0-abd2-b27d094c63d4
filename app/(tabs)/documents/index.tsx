import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
  Alert,
} from "react-native";
import { Plus, Search, FileText, Trash2, Eye, TestTube } from "lucide-react-native";
import * as DocumentPicker from "expo-document-picker";
import { router } from "expo-router";
import { useDocuments } from "@/hooks/useDocuments";

export default function DocumentsScreen() {
  const { documents, addDocument, deleteDocument } = useDocuments();
  const [isUploading, setIsUploading] = useState<boolean>(false);

  const createTestPDF = async () => {
    // Create a more comprehensive test PDF with base64 data
    const testPDFBase64 = "JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjIgMCBvYmoKPDwKL1R5cGUgL1BhZ2VzCi9LaWRzIFszIDAgUl0KL0NvdW50IDEKPD4KZW5kb2JqCjMgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCAyIDAgUgovTWVkaWFCb3ggWzAgMCA2MTIgNzkyXQovUmVzb3VyY2VzIDw8Ci9Gb250IDw8Ci9GMSA0IDAgUgo+Pgo+PgovQ29udGVudHMgNSAwIFIKPj4KZW5kb2JqCjQgMCBvYmoKPDwKL1R5cGUgL0ZvbnQKL1N1YnR5cGUgL1R5cGUxCi9CYXNlRm9udCAvSGVsdmV0aWNhCj4+CmVuZG9iago1IDAgb2JqCjw8Ci9MZW5ndGggMTQ0Cj4+CnN0cmVhbQpCVAovRjEgMjQgVGYKNzIgNzIwIFRkCihUZXN0IFBERiBEb2N1bWVudCkgVGoKMCAtNTAgVGQKL0YxIDEyIFRmCihUaGlzIGlzIGEgdGVzdCBQREYgZG9jdW1lbnQgY3JlYXRlZCBmb3IgZGVidWdnaW5nLikgVGoKMCAtMjAgVGQKKElmIHlvdSBjYW4gc2VlIHRoaXMgdGV4dCwgdGhlIFBERiB2aWV3ZXIgaXMgd29ya2luZyBjb3JyZWN0bHkuKSBUagpFVApzdHJlYW0KZW5kb2JqCnhyZWYKMCA2CjAwMDAwMDAwMDAgNjU1MzUgZiAKMDAwMDAwMDAwOSAwMDAwMCBuIAowMDAwMDAwMDU4IDAwMDAwIG4gCjAwMDAwMDAxMTUgMDAwMDAgbiAKMDAwMDAwMDI0NSAwMDAwMCBuIAowMDAwMDAwMzIyIDAwMDAwIG4gCnRyYWlsZXIKPDwKL1NpemUgNgovUm9vdCAxIDAgUgo+PgpzdGFydHhyZWYKNTE2CiUlRU9G";
    
    const testDocument = {
      id: `test-pdf-${Date.now()}`,
      name: "Test PDF Document.pdf",
      uri: `data:application/pdf;base64,${testPDFBase64}`,
      size: testPDFBase64.length,
      createdAt: new Date().toISOString(),
      isScanned: false,
    };
    
    console.log('Creating test PDF with base64 length:', testPDFBase64.length);
    console.log('Test PDF preview:', testPDFBase64.substring(0, 50));

    try {
      await addDocument(testDocument);
      console.log('Test PDF created successfully');
      
      // Automatically open the test PDF for immediate testing
      setTimeout(() => {
        router.push({
          pathname: "/(tabs)/documents/viewer",
          params: { documentId: testDocument.id },
        });
      }, 500);
    } catch (error) {
      console.error('Error creating test PDF:', error);
      if (Platform.OS === 'web') {
        alert('Error creating test PDF: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    }
  };

  const handleAddDocument = async () => {
    try {
      setIsUploading(true);
      const result = await DocumentPicker.getDocumentAsync({
        type: "application/pdf",
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        let documentUri = asset.uri;
        
        // On web, convert blob URL to base64 data URL for storage
        if (Platform.OS === 'web' && asset.uri.startsWith('blob:')) {
          try {
            const response = await fetch(asset.uri);
            const blob = await response.blob();
            
            // Convert blob to base64 data URL
            const base64DataUrl = await new Promise<string>((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = () => resolve(reader.result as string);
              reader.onerror = reject;
              reader.readAsDataURL(blob);
            });
            
            documentUri = base64DataUrl;
            console.log('Converted blob to data URL for web storage');
          } catch (conversionError) {
            console.error('Error converting blob to base64:', conversionError);
            throw new Error('Failed to process PDF file');
          }
        }
        
        await addDocument({
          id: Date.now().toString(),
          name: asset.name,
          uri: documentUri,
          size: asset.size || 0,
          createdAt: new Date().toISOString(),
        });
        
        console.log('Document added successfully:', asset.name);
      }
    } catch (error) {
      console.error("Document picker error:", error);
      if (Platform.OS === 'web') {
        alert('Error adding document: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteDocument = (id: string, name: string) => {
    if (!id?.trim() || !name?.trim()) return;
    if (Platform.OS === 'web') {
      const confirmed = typeof confirm === 'function' ? confirm(`Are you sure you want to delete "${name.trim()}"?`) : true;
      if (confirmed) deleteDocument(id.trim());
    } else {
      Alert.alert(
        'Delete Document',
        `Are you sure you want to delete "${name.trim()}"?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Delete', style: 'destructive', onPress: () => deleteDocument(id.trim()) },
        ]
      );
    }
  };

  const handleViewDocument = (document: any) => {
    if (!document?.id?.trim()) return;
    
    router.push({
      pathname: "/(tabs)/documents/viewer",
      params: { documentId: document.id.trim() },
    });
  };

  const formatFileSize = (bytes: number) => {
    if (typeof bytes !== 'number' || bytes < 0 || !Number.isFinite(bytes)) return "0 Bytes";
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.searchButton}>
          <Search color="#6B7280" size={20} />
          <Text style={styles.searchText}>Search documents...</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.addButton, isUploading && styles.addButtonDisabled]} 
          onPress={handleAddDocument}
          disabled={isUploading}
        >
          {isUploading ? (
            <ActivityIndicator color="#FFFFFF" size={24} />
          ) : (
            <Plus color="#FFFFFF" size={24} />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}

      >
        {documents.length === 0 ? (
          <View style={styles.emptyState}>
            <FileText color="#9CA3AF" size={64} />
            <Text style={styles.emptyTitle}>No Documents Yet</Text>
            <Text style={styles.emptyDescription}>
              Add your first PDF document to get started
            </Text>
            <View style={styles.emptyButtonContainer}>
              <TouchableOpacity style={styles.emptyButton} onPress={handleAddDocument}>
                <Plus color="#FFFFFF" size={20} />
                <Text style={styles.emptyButtonText}>Add Document</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.testButton} onPress={createTestPDF}>
                <TestTube color="#FFFFFF" size={18} />
                <Text style={styles.testButtonText}>Create Test PDF</Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <View style={styles.documentsList}>
            {documents.map((document) => (
              <View key={document.id} style={styles.documentCard}>
                <View style={styles.documentIcon}>
                  <FileText color="#3B82F6" size={24} />
                </View>
                
                <View style={styles.documentInfo}>
                  <Text style={styles.documentName} numberOfLines={2}>
                    {document.name}
                  </Text>
                  <Text style={styles.documentMeta}>
                    {formatFileSize(document.size)} • {new Date(document.createdAt).toLocaleDateString()}
                  </Text>
                </View>

                <View style={styles.documentActions}>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => {
                      if (document?.id?.trim()) {
                        handleViewDocument(document);
                      }
                    }}
                  >
                    <Eye color="#6B7280" size={20} />
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleDeleteDocument(document.id, document.name)}
                  >
                    <Trash2 color="#EF4444" size={20} />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  searchButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 12,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  searchText: {
    color: "#6B7280",
    fontSize: 16,
  },
  addButton: {
    backgroundColor: "#3B82F6",
    width: 48,
    height: 48,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  addButtonDisabled: {
    backgroundColor: "#9CA3AF",
  },
  content: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 40,
    paddingVertical: 80,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#1F2937",
    marginTop: 24,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
  },
  emptyButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#3B82F6",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  emptyButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  emptyButtonContainer: {
    flexDirection: "row",
    gap: 12,
    alignItems: "center",
  },
  testButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#10B981",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 6,
  },
  testButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
  documentsList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  documentCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  documentIcon: {
    width: 48,
    height: 48,
    backgroundColor: "#EFF6FF",
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 4,
  },
  documentMeta: {
    fontSize: 14,
    color: "#6B7280",
  },
  documentActions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
    backgroundColor: "#F3F4F6",
  },
});
