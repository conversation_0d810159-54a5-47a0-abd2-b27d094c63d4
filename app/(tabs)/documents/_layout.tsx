import { Stack } from "expo-router";

export default function DocumentsLayout() {
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: "#FFFFFF",
        },
        headerTintColor: "#1F2937",
        headerTitleStyle: {
          fontWeight: "600",
          fontSize: 18,
        },
      }}
    >
      <Stack.Screen 
        name="index" 
        options={{ 
          title: "My Documents",
          headerLargeTitle: true,
        }} 
      />
      <Stack.Screen 
        name="viewer" 
        options={{ 
          title: "PDF Viewer",
          presentation: "modal",
        }} 
      />
    </Stack>
  );
}