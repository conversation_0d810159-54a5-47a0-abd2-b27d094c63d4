import { Platform } from "react-native";
import * as FileSystem from "expo-file-system";
import { performOcrOnImages, concatenateOcrText, OCRPage, OCROptions } from "./ocr";
import { Document } from "@/hooks/useDocuments";

export function normalizeImageUri(input: string): string {
  if (!input) return input;
  if (input.startsWith('file://') || input.startsWith('data:')) return input;
  if (input.startsWith('/')) return `file://${input}`;
  if (input.startsWith('content://')) return input; // or resolve to temp file via FileSystem
  return input;
}

// Import PDF generation - will be available after package installation
let createPdf: any;
try {
  createPdf = require("react-native-pdf-from-image").createPdf;
} catch (error) {
  console.warn("[PDF] react-native-pdf-from-image not available yet. Install the package to enable PDF generation.");
}

function assertPdfModuleAvailable() {
  if (typeof createPdf !== 'function') {
    throw new Error("PDF generation module is not available. Install 'react-native-pdf-from-image' to enable PDF creation.");
  }
}

export async function readPdfAsBase64(uri: string): Promise<string> {
  if (!uri) throw new Error("Empty URI");

  if (Platform.OS === 'web') {
    if (uri.startsWith('data:application/pdf;base64,')) {
      return uri.split(',')[1];
    }
    if (uri.startsWith('data:')) {
      // Unexpected data URL
      const [, base64 = ''] = uri.split(',');
      return base64;
    }
    if (uri.startsWith('blob:')) {
      const response = await fetch(uri);
      const blob = await response.blob();
      const base64DataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
      const [, base64 = ''] = base64DataUrl.split(',');
      return base64;
    }
    throw new Error('Unsupported web URI format for PDF');
  }

  // Native: read as base64 from file URI
  const base64 = await FileSystem.readAsStringAsync(uri, {
    encoding: FileSystem.EncodingType.Base64,
  });
  return base64;
}

export function isLargeFile(size: number, thresholdMB = 20): boolean {
  if (!size || !Number.isFinite(size)) return false;
  return size / (1024 * 1024) > thresholdMB;
}

export function getPdfFileName(): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  return `document_${timestamp}.pdf`;
}

export async function generatePdfFromImages(
  imageUris: string[],
  options: {
    nameOverride?: string;
    paperSize?: 'A4' | 'A3' | 'LETTER' | 'LEGAL';
    customPaperSize?: { width: number; height: number };
  } = {}
): Promise<string> {
  if (!imageUris || imageUris.length === 0) {
    throw new Error("No images provided for PDF generation");
  }

  assertPdfModuleAvailable();

  const outputDirectory = FileSystem.documentDirectory ?? FileSystem.cacheDirectory;
  if (!outputDirectory) {
    throw new Error('File system directory is not available for PDF generation on this platform.');
  }

  const temporaryFiles: string[] = [];

  try {
    // Generate a unique filename for the PDF
    const { nameOverride, paperSize = 'A4', customPaperSize } = options;
    let pdfFileName = nameOverride || getPdfFileName();
    // Ensure .pdf extension is enforced
    if (!pdfFileName.toLowerCase().endsWith('.pdf')) {
      pdfFileName += '.pdf';
    }
    const pdfPath = `${outputDirectory}${pdfFileName}`;

    // Convert image URIs to absolute file paths that the PDF library requires
    const absoluteImagePaths = await Promise.all(
      imageUris.map(async (uri) => {
        const normalized = normalizeImageUri(uri);

        if (normalized.startsWith('file://')) {
          return normalized;
        }

        if (normalized.startsWith('data:')) {
          const base64Data = normalized.split(',')[1];
          const tempDirectory = FileSystem.cacheDirectory ?? FileSystem.documentDirectory;
          if (!tempDirectory) {
            throw new Error('Unable to resolve a writable directory for temporary image storage.');
          }

          const tempFileName = `temp_image_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
          const tempPath = `${tempDirectory}${tempFileName}`;
          await FileSystem.writeAsStringAsync(tempPath, base64Data, {
            encoding: FileSystem.EncodingType.Base64,
          });
          temporaryFiles.push(tempPath);
          return tempPath;
        }

        // Android content provider URI: copy to a temp file
        if (normalized.startsWith('content://')) {
          // Copy content:// URI to temp file
          const tempDirectory = FileSystem.cacheDirectory ?? FileSystem.documentDirectory;
          if (!tempDirectory) {
            throw new Error('Unable to resolve a writable directory for temporary image storage.');
          }
          const tempFileName = `temp_image_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
          const tempPath = `${tempDirectory}${tempFileName}`;
          await FileSystem.copyAsync({ from: normalized, to: tempPath });
          temporaryFiles.push(tempPath);
          return tempPath;
        }

        // Web blob URI: fetch and write to a temp file as base64
        if (normalized.startsWith('blob:')) {
          const tempDirectory = FileSystem.cacheDirectory ?? FileSystem.documentDirectory;
          if (!tempDirectory) {
            throw new Error('Unable to resolve a writable directory for temporary image storage.');
          }

          const tempFileName = `temp_blob_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
          const tempPath = `${tempDirectory}${tempFileName}`;

          const response = await fetch(normalized);
          const blob = await response.blob();
          const arrayBuffer = await blob.arrayBuffer();
          const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

          await FileSystem.writeAsStringAsync(tempPath, base64Data, {
            encoding: FileSystem.EncodingType.Base64,
          });
          temporaryFiles.push(tempPath);
          return tempPath;
        }

        // At this point, the URI scheme is unsupported
        throw new Error(`Unsupported image URI format: ${uri}. Supported: file://, data:, blob:, content://, or absolute path`);
      })
    );

    // Generate PDF from images with customizable paper size
    const pdfOptions: any = {
      images: absoluteImagePaths,
      outputPath: pdfPath,
      paperSize: paperSize,
    };

    if (customPaperSize) {
      pdfOptions.customPaperSize = customPaperSize;
    }

    await createPdf(pdfOptions);

    return pdfPath;
  } catch (error) {
    console.error('PDF generation failed:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    // Clean up temporary files if any were created
    await Promise.all(
      temporaryFiles.map(async (path) => {
        try {
          await FileSystem.deleteAsync(path, { idempotent: true });
        } catch (cleanupError) {
          console.warn('Failed to clean up temporary file:', path, cleanupError);
        }
      })
    );
  }
}

// Progress reporting interface
export interface DocumentCreationProgress {
  step: string;
  percentage: number;
  currentItem?: number;
  totalItems?: number;
}

// Cancellation token interface
export interface CancellationToken {
  cancelled: boolean;
}

/**
 * Creates a document with both PDF generation and OCR processing
 * @param imageUris - Array of image URIs to process
 * @param options - Options for OCR processing, document creation, progress reporting, and cancellation
 * @returns Promise<Document> - Complete document with PDF and OCR data
 */
export async function createDocumentWithOcr(
  imageUris: string[],
  options: {
    skipOcr?: boolean;
    ocrOptions?: OCROptions;
    documentName?: string;
    onProgress?: (progress: DocumentCreationProgress) => void;
    cancelToken?: CancellationToken;
  } = {}
): Promise<Document> {
  const startTime = Date.now();
  const { skipOcr = false, ocrOptions = {}, documentName, onProgress, cancelToken } = options;

  if (!imageUris || imageUris.length === 0) {
    throw new Error("No images provided for document creation");
  }

  // Helper function to check cancellation
  const checkCancellation = () => {
    if (cancelToken?.cancelled) {
      throw new Error("Document creation was cancelled by user");
    }
  };

  // Helper function to report progress
  const reportProgress = (step: string, percentage: number, currentItem?: number, totalItems?: number) => {
    if (onProgress) {
      try {
        onProgress({ step, percentage, currentItem, totalItems });
      } catch (error) {
        console.warn('[Document Creation] Progress callback error:', error);
      }
    }
  };

  console.log(`[Document Creation] Starting document creation with ${imageUris.length} images`);
  reportProgress("Initializing document creation", 0);

  let pdfPath: string = '';
  let ocrPages: OCRPage[] = [];
  let ocrText = '';
  let ocrProcessed = false;
  let ocrEdited = false;

  try {
    // Check for cancellation before starting
    checkCancellation();

    // Step 1: Generate PDF from images
    reportProgress("Generating PDF from images", 10);
    const pdfStartTime = Date.now();
    pdfPath = await generatePdfFromImages(imageUris, { nameOverride: documentName });
    const pdfGenerationTime = Date.now() - pdfStartTime;
    console.log(`[Document Creation] PDF generated in ${pdfGenerationTime}ms`);

    // Check for cancellation after PDF generation
    checkCancellation();
    reportProgress("PDF generation complete", skipOcr ? 80 : 30);

    // Step 2: Perform OCR processing (if not skipped)
    if (!skipOcr) {
      try {
        reportProgress("Starting text extraction", 35);
        const ocrStartTime = Date.now();

        // Create progress callback for OCR processing
        const ocrProgressCallback = (progress: { completed: number; total: number; currentImage?: string }) => {
          const ocrProgressPercentage = 35 + Math.round((progress.completed / progress.total) * 45); // 35% to 80%
          reportProgress(
            `Extracting text from page ${progress.completed + 1} of ${progress.total}`,
            ocrProgressPercentage,
            progress.completed + 1,
            progress.total
          );
        };

        const ocrResult = await performOcrOnImages(imageUris, {
          ...ocrOptions,
          onProgress: ocrProgressCallback,
          cancelToken: cancelToken
        });

        ocrPages = ocrResult.results;
        if (ocrResult.errors.length > 0 && ocrOptions.enableLogging !== false) {
          console.warn(`[Document Creation] OCR completed with ${ocrResult.errors.length} errors:`, ocrResult.errors);
        }
        ocrText = concatenateOcrText(ocrPages);
        ocrProcessed = true;
        const ocrProcessingTime = Date.now() - ocrStartTime;
        console.log(`[Document Creation] OCR processed in ${ocrProcessingTime}ms`);
        reportProgress("Text extraction complete", 80);
      } catch (ocrError) {
        // Check if error is due to cancellation
        if (ocrError instanceof Error && ocrError.message.includes('cancelled')) {
          throw ocrError; // Re-throw cancellation errors
        }
        console.warn('[Document Creation] OCR processing failed, continuing with PDF only:', ocrError);
        reportProgress("Text extraction failed, continuing with PDF only", 80);
        // OCR failed but PDF succeeded - continue with PDF-only document
      }
    }

    // Check for cancellation before finalizing
    checkCancellation();

    // Step 3: Get PDF file info
    reportProgress("Finalizing document", 85);
    const fileInfo = await FileSystem.getInfoAsync(pdfPath);
    if (!fileInfo.exists) {
      throw new Error("Generated PDF file does not exist");
    }

    // Step 4: Create document object
    reportProgress("Creating document metadata", 90);
    const finalDocumentName = documentName || getPdfFileName();
    const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const document: Document = {
      id: documentId,
      name: finalDocumentName,
      uri: pdfPath,
      size: fileInfo.size || 0,
      createdAt: new Date().toISOString(),
      isScanned: true,
      pageCount: imageUris.length,
      originalImages: imageUris,
      // OCR data
      ocrText,
      ocrPages,
      ocrProcessed,
      ocrEdited,
    };

    const totalTime = Date.now() - startTime;
    console.log(`[Document Creation] Document created successfully in ${totalTime}ms`);

    if (ocrProcessed && ocrText) {
      console.log(`[Document Creation] Document includes OCR data: ${ocrText.length} characters extracted`);
    }

    reportProgress("Document creation complete", 100);
    return document;
  } catch (error) {
    const totalTime = Date.now() - startTime;
    const isCancellation = error instanceof Error && error.message.includes('cancelled');

    if (isCancellation) {
      console.log(`[Document Creation] Document creation cancelled after ${totalTime}ms`);
      reportProgress("Document creation cancelled", 0);
    } else {
      console.error(`[Document Creation] Document creation failed after ${totalTime}ms:`, error);
      reportProgress("Document creation failed", 0);
    }

    // Clean up PDF if it was created but an error occurred later
    if (pdfPath && await FileSystem.getInfoAsync(pdfPath).then(info => info.exists).catch(() => false)) {
      try {
        await FileSystem.deleteAsync(pdfPath, { idempotent: true });
        console.log('[Document Creation] Cleaned up PDF file after error');
      } catch (cleanupError) {
        console.warn('[Document Creation] Failed to clean up PDF file:', cleanupError);
      }
    }

    if (isCancellation) {
      throw error; // Re-throw cancellation errors as-is
    }

    throw new Error(`Failed to create document with OCR: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Updates OCR text in an existing document
 * @param documentId - ID of the document to update
 * @param newOcrText - New OCR text content
 * @returns Promise<Document> - Updated document
 */
export async function updateDocumentOcrText(
  documentId: string,
  newOcrText: string
): Promise<Document> {
  if (!documentId?.trim()) {
    throw new Error("Invalid document ID");
  }

  if (!newOcrText) {
    throw new Error("OCR text cannot be empty");
  }

  try {
    // This would typically be handled by the useDocuments hook
    // For now, we'll throw an error indicating this should be handled by the hook
    throw new Error("Use the updateDocument function from useDocuments hook to update OCR text");
  } catch (error) {
    console.error('[Document Update] Failed to update OCR text:', error);
    throw error;
  }
}

