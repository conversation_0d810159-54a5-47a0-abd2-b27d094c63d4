require("@testing-library/jest-native/extend-expect");

// Basic mock for expo-router (avoid importing actual to prevent JSX transforms)
jest.mock("expo-router", () => ({
  router: {
    push: jest.fn(),
    back: jest.fn(),
    dismiss: jest.fn(),
  },
  useLocalSearchParams: jest.fn(() => ({})),
}));

// Mock WebView to a simple component that renders nothing
jest.mock("react-native-webview", () => ({
  WebView: () => null,
}));

// Mock expo document picker to avoid ESM import issues and side effects
jest.mock("expo-document-picker", () => ({
  getDocumentAsync: jest.fn(async () => ({ canceled: true, assets: [] })),
}));

// Mock expo-file-system to avoid native calls during tests
jest.mock("expo-file-system", () => ({
  EncodingType: { Base64: "base64" },
  readAsStringAsync: jest.fn(async () => ""),
  writeAsStringAsync: jest.fn(async () => {}),
  deleteAsync: jest.fn(async () => {}),
  getInfoAsync: jest.fn(async () => ({ exists: true, size: 0 })),
  cacheDirectory: "file:///cache/",
  documentDirectory: "file:///documents/",
  copyAsync: jest.fn(async () => {}),
  downloadAsync: jest.fn(async (_url, to) => ({ uri: to, status: 200 })),
}));

// Mock expo-asset to avoid ESM import issues
jest.mock("expo-asset", () => ({
  Asset: {
    fromModule: jest.fn(() => ({
      downloadAsync: jest.fn(async () => ({ localUri: "mock-asset-uri" })),
      uri: "mock-asset-uri",
      localUri: "mock-asset-uri", // Provide localUri immediately to avoid downloadAsync call
    })),
  },
}));

// Mock expo-sharing
jest.mock("expo-sharing", () => ({
  shareAsync: jest.fn(async () => {}),
  isAvailableAsync: jest.fn(async () => true),
}));

// Mock expo-image-picker
jest.mock("expo-image-picker", () => ({
  launchImageLibraryAsync: jest.fn(async () => ({ canceled: true, assets: [] })),
  launchCameraAsync: jest.fn(async () => ({ canceled: true, assets: [] })),
  requestMediaLibraryPermissionsAsync: jest.fn(async () => ({ status: "granted" })),
  requestCameraPermissionsAsync: jest.fn(async () => ({ status: "granted" })),
  MediaTypeOptions: { Images: "Images" },
}));

// In-memory AsyncStorage mock
const memoryStore = {};
jest.mock("@react-native-async-storage/async-storage", () => ({
  setItem: jest.fn(async (key, value) => {
    memoryStore[key] = value;
  }),
  getItem: jest.fn(async (key) => memoryStore[key] ?? null),
  removeItem: jest.fn(async (key) => {
    delete memoryStore[key];
  }),
  clear: jest.fn(async () => {
    Object.keys(memoryStore).forEach((k) => delete memoryStore[k]);
  }),
}));
