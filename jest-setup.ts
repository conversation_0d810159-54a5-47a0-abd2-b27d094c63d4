// Declare <PERSON>st global for TypeScript without adding @types/jest
// This keeps the setup file simple and avoids TS namespace errors.
declare const jest: any;

import "@testing-library/jest-native/extend-expect";

// Basic mock for expo-router (avoid importing actual to prevent JSX transforms)
jest.mock("expo-router", () => ({
  router: {
    push: jest.fn(),
    back: jest.fn(),
    dismiss: jest.fn(),
  },
  useLocalSearchParams: jest.fn(() => ({})),
}));

// Mock WebView to a simple component that renders its props
jest.mock("react-native-webview", () => ({
  WebView: () => null,
}));

// Mock expo document picker to avoid ESM import issues and side effects
jest.mock("expo-document-picker", () => ({
  getDocumentAsync: jest.fn(async () => ({ canceled: true, assets: [] })),
}));

// Mock expo-file-system to avoid ESM transform and native calls
jest.mock("expo-file-system", () => ({
  EncodingType: { Base64: "base64" },
  readAsStringAsync: jest.fn(async (_uri: string, _opts?: any) => ""),
}));

// In-memory AsyncStorage mock
const memoryStore: Record<string, string> = {};
jest.mock("@react-native-async-storage/async-storage", () => ({
  setItem: jest.fn(async (key: string, value: string) => {
    memoryStore[key] = value;
  }),
  getItem: jest.fn(async (key: string) => memoryStore[key] ?? null),
  removeItem: jest.fn(async (key: string) => {
    delete memoryStore[key];
  }),
  clear: jest.fn(async () => {
    Object.keys(memoryStore).forEach((k) => delete memoryStore[k]);
  }),
}));
