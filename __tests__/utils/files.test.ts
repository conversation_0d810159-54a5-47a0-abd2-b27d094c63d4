import { isLargeFile, readPdfAsBase64 } from '@/utils/files';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

describe('utils/files', () => {
  test('isLargeFile detects threshold correctly', () => {
    expect(isLargeFile(0)).toBe(false);
    expect(isLargeFile(5 * 1024 * 1024, 5)).toBe(false); // exactly threshold treated as not large
    expect(isLargeFile(6 * 1024 * 1024, 5)).toBe(true);
    expect(isLargeFile(Number.NaN as any)).toBe(false);
  });

  test('readPdfAsBase64 handles web data URL', async () => {
    // @ts-ignore
    Platform.OS = 'web';
    const base64 = 'JVBERi0xLjQK...';
    const url = `data:application/pdf;base64,${base64}`;
    await expect(readPdfAsBase64(url)).resolves.toBe(base64);
  });

  test('readPdfAsBase64 reads native file via FileSystem', async () => {
    // @ts-ignore
    Platform.OS = 'ios';
    const spy = jest.spyOn(FileSystem, 'readAsStringAsync').mockResolvedValue('AAAABASE64');
    const out = await readPdfAsBase64('file:///path/to/file.pdf');
    expect(out).toBe('AAAABASE64');
    spy.mockRestore();
  });
});

