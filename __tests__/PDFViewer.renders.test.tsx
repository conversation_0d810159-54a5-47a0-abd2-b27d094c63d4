import React from "react";
import { render } from "@testing-library/react-native";

// Adjust mocks for this test instance
const routerMod = require("expo-router");
(routerMod.useLocalSearchParams as jest.Mock).mockReturnValue({ documentId: "doc-1" });

jest.mock("@/hooks/useDocuments", () => ({
  useDocuments: () => ({
    documents: [
      {
        id: "doc-1",
        name: "Test PDF Document.pdf",
        // Data URL prefix ensures isDataUrl=true and isImage=false
        uri: "data:application/pdf;base64,QUJDREVGR0hJSktMTU5PUFFSU1RVVldY",
        size: 988,
        createdAt: new Date().toISOString(),
        isScanned: false,
      },
    ],
    deleteDocument: jest.fn(),
  }),
}));

import PDFViewerScreen from "@/app/(tabs)/documents/viewer";

describe("PDFViewerScreen", () => {
  it("renders without re-render loop for data URL PDF", () => {
    const { getByText } = render(<PDFViewerScreen />);
    // While loading, it shows a loading UI or document title later; no crash means success.
    // We avoid strict assertions on WebView internals due to mocks.
    expect(true).toBe(true);
  });
});

