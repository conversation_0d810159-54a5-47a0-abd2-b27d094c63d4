import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ScanScreen from '@/app/(tabs)/scan/index';
import { useDocuments } from '@/hooks/useDocuments';
import { createDocumentWithOcr } from '@/utils/files';
import DocumentScanner from 'react-native-document-scanner-plugin';
import * as ImagePicker from 'expo-image-picker';

// Mock dependencies
jest.mock('@/hooks/useDocuments');
jest.mock('@/utils/files');
jest.mock('react-native-document-scanner-plugin', () => ({
  default: {
    scanDocument: jest.fn(),
  },
  scanDocument: jest.fn(),
}));
jest.mock('expo-image-picker');
jest.mock('@react-native-async-storage/async-storage');
jest.mock('expo-router', () => ({
  router: {
    push: jest.fn(),
  },
}));

const mockUseDocuments = useDocuments as jest.MockedFunction<typeof useDocuments>;
const mockCreateDocumentWithOcr = createDocumentWithOcr as jest.MockedFunction<typeof createDocumentWithOcr>;
const mockDocumentScanner = DocumentScanner as jest.Mocked<typeof DocumentScanner>;
const mockImagePicker = ImagePicker as jest.Mocked<typeof ImagePicker>;
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('ScanScreen Integration Tests', () => {
  const mockAddDocument = jest.fn();
  const mockDocument = {
    id: 'test-doc-1',
    name: 'Test Document.pdf',
    uri: '/path/to/document.pdf',
    size: 1024000,
    createdAt: new Date().toISOString(),
    isScanned: true,
    pageCount: 2,
    originalImages: ['image1.jpg', 'image2.jpg'],
    ocrText: 'Sample OCR text',
    ocrPages: [],
    ocrProcessed: true,
    ocrEdited: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockUseDocuments.mockReturnValue({
      addDocument: mockAddDocument,
      documents: [],
      updateDocument: jest.fn(),
      deleteDocument: jest.fn(),
      isLoading: false,
      error: null,
    });

    mockCreateDocumentWithOcr.mockResolvedValue(mockDocument);
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue(undefined);
    
    // Setup document scanner mock for both default and named export
    const mockScanDocument = jest.fn().mockResolvedValue({
      scannedImages: ['file://image1.jpg', 'file://image2.jpg'],
      status: 'success',
    });

    mockDocumentScanner.scanDocument = mockScanDocument;
    (DocumentScanner as any).default = { scanDocument: mockScanDocument };

    mockImagePicker.requestMediaLibraryPermissionsAsync.mockResolvedValue({
      status: 'granted',
      expires: 'never',
      granted: true,
      canAskAgain: true,
    });

    mockImagePicker.launchImageLibraryAsync.mockResolvedValue({
      canceled: false,
      assets: [
        { uri: 'file://selected1.jpg', width: 800, height: 600 },
        { uri: 'file://selected2.jpg', width: 800, height: 600 },
      ],
    });

    // Reset Alert mock
    (Alert.alert as jest.Mock).mockClear();
  });

  describe('OCR Skip Option', () => {
    it('should load OCR skip preference on mount', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('true');

      const { getByTestId } = render(<ScanScreen />);

      await waitFor(() => {
        expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('@scan_skip_ocr');
      });
    });

    it('should save OCR skip preference when changed', async () => {
      const { getByTestId, getByRole } = render(<ScanScreen />);

      // Open settings using testID
      const settingsButton = getByTestId('settings-button');
      fireEvent.press(settingsButton);

      await waitFor(() => {
        const skipSwitch = getByRole('switch');
        fireEvent(skipSwitch, 'valueChange', true);
      });

      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('@scan_skip_ocr', 'true');
    });

    it('should pass skipOcr option to createDocumentWithOcr', async () => {
      // Set skip OCR to true
      mockAsyncStorage.getItem.mockResolvedValue('true');

      const { getByText } = render(<ScanScreen />);

      // Wait for the component to load the OCR preference
      await waitFor(() => {
        expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('@scan_skip_ocr');
      });

      // Trigger camera scanning
      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        expect(mockCreateDocumentWithOcr).toHaveBeenCalledWith(
          expect.any(Array),
          expect.objectContaining({
            skipOcr: true,
          })
        );
      });
    });
  });

  describe('Progress Reporting', () => {
    it('should show progress during document creation', async () => {
      let progressCallback: ((progress: any) => void) | undefined;
      const progressSteps: any[] = [];

      mockCreateDocumentWithOcr.mockImplementation(async (uris, options) => {
        progressCallback = options?.onProgress;

        // Simulate progress updates with delays
        if (progressCallback) {
          const step1 = { step: 'Generating PDF from images', percentage: 10 };
          progressCallback(step1);
          progressSteps.push(step1);
          await new Promise(resolve => setTimeout(resolve, 50));

          const step2 = { step: 'Extracting text from page 1 of 2', percentage: 50, currentItem: 1, totalItems: 2 };
          progressCallback(step2);
          progressSteps.push(step2);
          await new Promise(resolve => setTimeout(resolve, 50));

          const step3 = { step: 'Document creation complete', percentage: 100 };
          progressCallback(step3);
          progressSteps.push(step3);
        }

        return mockDocument;
      });

      const { getByText } = render(<ScanScreen />);

      // Trigger camera scanning
      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      // Wait for processing to complete and verify progress steps were called
      await waitFor(() => {
        expect(progressSteps).toHaveLength(3);
        expect(progressSteps[0]).toEqual({ step: 'Generating PDF from images', percentage: 10 });
        expect(progressSteps[1]).toEqual({ step: 'Extracting text from page 1 of 2', percentage: 50, currentItem: 1, totalItems: 2 });
        expect(progressSteps[2]).toEqual({ step: 'Document creation complete', percentage: 100 });
      });
    });

    it('should show progress bar with correct percentage', async () => {
      let progressCallback: ((progress: any) => void) | undefined;

      mockCreateDocumentWithOcr.mockImplementation(async (uris, options) => {
        progressCallback = options?.onProgress;

        if (progressCallback) {
          progressCallback({ step: 'Processing', percentage: 75 });
          // Add delay to keep the progress state visible
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        return mockDocument;
      });

      const { getByText } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        expect(getByText('75%')).toBeTruthy();
      });
    });

    it('should show page progress for multi-page documents', async () => {
      let progressCallback: ((progress: any) => void) | undefined;

      mockCreateDocumentWithOcr.mockImplementation(async (uris, options) => {
        progressCallback = options?.onProgress;

        if (progressCallback) {
          progressCallback({
            step: 'Extracting text',
            percentage: 60,
            currentItem: 2,
            totalItems: 3
          });
          // Add delay to keep the progress state visible
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        return mockDocument;
      });

      const { getByText } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        expect(getByText('Page 2 of 3')).toBeTruthy();
      });
    });
  });

  describe('Cancellation Support', () => {
    it('should show cancel button during processing', async () => {
      mockCreateDocumentWithOcr.mockImplementation(async (uris, options) => {
        // Simulate long-running operation
        await new Promise(resolve => setTimeout(resolve, 100));
        return mockDocument;
      });

      const { getByText, queryByTestId } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        const cancelButton = queryByTestId('cancel-processing');
        expect(cancelButton).toBeTruthy();
      });
    });

    it('should handle cancellation correctly', async () => {
      let cancelToken: { cancelled: boolean } | undefined;

      mockCreateDocumentWithOcr.mockImplementation(async (uris, options) => {
        cancelToken = options?.cancelToken;
        
        // Simulate cancellation
        if (cancelToken) {
          cancelToken.cancelled = true;
        }
        
        throw new Error('Document creation was cancelled by user');
      });

      const { getByText, queryByRole, queryByTestId } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        const cancelButton = queryByTestId('cancel-processing');
        if (cancelButton) {
          fireEvent.press(cancelButton);
        }
      });

      // Should show cancellation result
      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Cancelled',
          'Document processing was cancelled.',
          expect.any(Array)
        );
      });
    });

    it('should reset state after cancellation', async () => {
      mockCreateDocumentWithOcr.mockRejectedValue(new Error('Document creation was cancelled by user'));

      const { getByText, queryByText } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Cancelled',
          'Document processing was cancelled.',
          expect.any(Array)
        );
      });

      // Processing UI should be hidden
      expect(queryByText('Creating PDF')).toBeFalsy();
    });
  });

  describe('Error Handling', () => {
    it('should handle document scanner errors', async () => {
      mockDocumentScanner.scanDocument.mockRejectedValue(new Error('Scanner failed'));

      const { getByText } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Error',
          expect.stringContaining('Failed to scan document'),
          expect.any(Array)
        );
      });
    });

    it('should handle document creation errors', async () => {
      mockCreateDocumentWithOcr.mockRejectedValue(new Error('PDF generation failed'));

      const { getByText } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Error',
          expect.stringContaining('Failed to scan document'),
          expect.any(Array)
        );
      });
    });

    it('should handle image picker permission denied', async () => {
      mockImagePicker.requestMediaLibraryPermissionsAsync.mockResolvedValue({
        status: 'denied',
        expires: 'never',
        granted: false,
        canAskAgain: true,
      });

      const { getByText } = render(<ScanScreen />);

      const libraryButton = getByText('Photo Library');
      fireEvent.press(libraryButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Permission needed',
          'Photo library permission is required'
        );
      });
    });
  });

  describe('Success Scenarios', () => {
    it('should show success message with OCR results', async () => {
      const { getByText } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Success',
          expect.stringContaining('text extracted'),
          expect.any(Array)
        );
      });
    });

    it('should show success message without OCR when skipped', async () => {
      const documentWithoutOCR = { ...mockDocument, ocrProcessed: false, ocrText: null };
      mockCreateDocumentWithOcr.mockResolvedValue(documentWithoutOCR);
      mockAsyncStorage.getItem.mockResolvedValue('true'); // Skip OCR

      const { getByText } = render(<ScanScreen />);

      // Wait for the component to load the OCR preference
      await waitFor(() => {
        expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('@scan_skip_ocr');
      });

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Success',
          expect.stringContaining('text extraction skipped'),
          expect.any(Array)
        );
      });
    });

    it('should add document to storage on success', async () => {
      const { getByText } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      await waitFor(() => {
        expect(mockAddDocument).toHaveBeenCalledWith(mockDocument);
      });
    });
  });

  describe('UI State Management', () => {
    it('should disable scan buttons during processing', async () => {
      mockCreateDocumentWithOcr.mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return mockDocument;
      });

      const { getByText } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      const libraryButton = getByText('Photo Library');

      fireEvent.press(cameraButton);

      await waitFor(() => {
        expect(cameraButton).toBeDisabled();
        expect(libraryButton).toBeDisabled();
      });
    });

    it('should re-enable buttons after processing completes', async () => {
      // Add a small delay to make the processing state visible
      mockCreateDocumentWithOcr.mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return mockDocument;
      });

      const { getByText } = render(<ScanScreen />);

      const cameraButton = getByText('Camera Scanner');
      fireEvent.press(cameraButton);

      // Wait for processing to complete by checking for success alert
      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Success',
          expect.any(String),
          expect.any(Array)
        );
      }, { timeout: 2000 });

      // After success, buttons should be re-enabled (processing state should be idle)
      // We can verify this by checking that we can press the button again
      expect(() => fireEvent.press(cameraButton)).not.toThrow();
    });

    it('should handle settings modal open/close', async () => {
      const { getByText, queryByText, getByTestId } = render(<ScanScreen />);

      // Open settings using testID
      const settingsButton = getByTestId('settings-button');
      fireEvent.press(settingsButton);

      await waitFor(() => {
        expect(queryByText('Scan Settings')).toBeTruthy();
      });

      // Close settings
      const doneButton = getByText('Done');
      fireEvent.press(doneButton);

      await waitFor(() => {
        expect(queryByText('Scan Settings')).toBeFalsy();
      });
    });
  });
});
