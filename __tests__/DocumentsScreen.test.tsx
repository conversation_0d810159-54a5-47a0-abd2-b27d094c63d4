import React from "react";
import { render, fireEvent, screen } from "@testing-library/react-native";
import DocumentsScreen from "@/app/(tabs)/documents/index";

// Track mock calls across tests
const mockAddDoc = jest.fn().mockResolvedValue(undefined);
const mockDelDoc = jest.fn().mockResolvedValue(undefined);

// Mock the hook to control its state
jest.mock("@/hooks/useDocuments", () => ({
  useDocuments: () => ({
    documents: [],
    addDocument: mockAddDoc,
    deleteDocument: mockDelDoc,
    isLoading: false,
    error: null,
    refetch: jest.fn(),
  }),
}));

// Capture router for assertions if needed later
const mockedRouter = require("expo-router").router;

describe("DocumentsScreen", () => {
  test("renders empty state and triggers create test PDF", async () => {
    jest.useFakeTimers();
    const { getByText } = render(<DocumentsScreen />);

    // Empty state text
    expect(getByText("No Documents Yet")).toBeTruthy();
    expect(getByText("Add your first PDF document to get started")).toBeTruthy();

    // Create Test PDF button
    const createBtn = getByText("Create Test PDF");
    fireEvent.press(createBtn);
    // Flush the navigation timeout to avoid leaking timers
    jest.runOnlyPendingTimers();
    jest.useRealTimers();

    // The create flow adds a document and then navigates after a timeout.
    // We only assert navigation intent is possible (push is called later) and that no crash occurs.
    // Optionally advance timers if needed (but we didn’t enable fake timers here).
    expect(mockAddDoc).toHaveBeenCalled();
  });
});
