import React from 'react';
import { render } from '@testing-library/react-native';
import { StyleSheet } from 'react-native';
import TextOverlay, { mapBlockToOverlayRect } from '@/components/pdf/TextOverlay';
import { OCRPage, OCRBlock } from '@/utils/ocr';

// Mock data for testing
const mockOCRBlock: OCRBlock = {
  text: 'Sample text',
  boundingBox: {
    left: 100,
    top: 50,
    width: 200,
    height: 30,
  },
  confidence: 0.95,
  cornerPoints: [
    { x: 100, y: 50 },
    { x: 300, y: 50 },
    { x: 300, y: 80 },
    { x: 100, y: 80 },
  ],
};

const mockOCRPage: OCRPage = {
  fullText: 'Sample text',
  blocks: [mockOCRBlock],
  width: 800,
  height: 600,
};

const mockPageMetrics = new Map([
  [0, {
    width: 400,
    height: 300,
    offsetX: 50,
    offsetY: 25,
    naturalWidth: 800,
    naturalHeight: 600,
    scale: 1.0,
    rotation: 0,
  }],
]);

describe('TextOverlay Coordinate Mapping', () => {
  const defaultProps = {
    ocrPages: [mockOCRPage],
    currentPage: 0,
    zoom: 1,
    rotation: 0,
    visible: true,
    pageMetrics: mockPageMetrics,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic coordinate scaling', () => {
    it('should scale coordinates correctly with different zoom levels', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} zoom={2} />
      );

      // Test with zoom level 2
      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} zoom={2} />)).not.toThrow();

      // Test with zoom level 0.5
      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} zoom={0.5} />)).not.toThrow();
    });

    it('should render blocks with correct coordinate styles', () => {
      const { getByTestId } = render(
        <TextOverlay {...defaultProps} />
      );

      const block = getByTestId('ocr-block-0');
      const style = StyleSheet.flatten(block.props.style);

      // Check that coordinates are applied correctly
      // Based on the actual implementation: x = 100 * (400/800) + 50 offset = 100
      expect(style.left).toBeCloseTo(100); // 100 * (400/800) + 50 offset
      expect(style.top).toBeCloseTo(50); // 50 * (300/600) + 25 offset
      expect(style.width).toBeCloseTo(100); // 200 * (400/800)
      expect(style.height).toBeCloseTo(15); // 30 * (300/600)
    });

    it('should handle proper scale factors based on page metrics', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} />
      );

      // Component should render without errors
      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} />)).not.toThrow();
    });

    it('should handle missing page metrics gracefully', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} pageMetrics={undefined} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} pageMetrics={undefined} />)).not.toThrow();

      // Test with empty page metrics
      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} pageMetrics={new Map()} />)).not.toThrow();
    });
  });

  describe('Rotation transformations', () => {
    it('should handle 90-degree rotation correctly', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} rotation={90} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} rotation={90} />)).not.toThrow();
    });

    it('should handle 180-degree rotation correctly', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} rotation={180} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} rotation={180} />)).not.toThrow();
    });

    it('should handle 270-degree rotation correctly', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} rotation={270} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} rotation={270} />)).not.toThrow();
    });

    it('should handle arbitrary rotation angles', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} rotation={45} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} rotation={45} />)).not.toThrow();
    });
  });

  describe('Page offset calculations', () => {
    it('should apply page offsets correctly', () => {
      const offsetPageMetrics = new Map([
        [0, {
          ...mockPageMetrics.get(0)!,
          offsetX: 100,
          offsetY: 150,
        }],
      ]);

      const renderResult = render(
        <TextOverlay {...defaultProps} pageMetrics={offsetPageMetrics} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} pageMetrics={offsetPageMetrics} />)).not.toThrow();
    });

    it('should handle negative offsets', () => {
      const negativeOffsetMetrics = new Map([
        [0, {
          ...mockPageMetrics.get(0)!,
          offsetX: -50,
          offsetY: -25,
        }],
      ]);

      const renderResult = render(
        <TextOverlay {...defaultProps} pageMetrics={negativeOffsetMetrics} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} pageMetrics={negativeOffsetMetrics} />)).not.toThrow();
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle invalid OCR dimensions gracefully', () => {
      const invalidOCRPage: OCRPage = {
        ...mockOCRPage,
        width: 0,
        height: 0,
      };

      const renderResult = render(
        <TextOverlay {...defaultProps} ocrPages={[invalidOCRPage]} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} ocrPages={[invalidOCRPage]} />)).not.toThrow();
    });

    it('should handle missing bounding box data', () => {
      const invalidBlock: OCRBlock = {
        ...mockOCRBlock,
        boundingBox: {
          left: NaN,
          top: NaN,
          width: NaN,
          height: NaN,
        },
      };

      const invalidOCRPage: OCRPage = {
        ...mockOCRPage,
        blocks: [invalidBlock],
      };

      const renderResult = render(
        <TextOverlay {...defaultProps} ocrPages={[invalidOCRPage]} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} ocrPages={[invalidOCRPage]} />)).not.toThrow();
    });

    it('should handle empty OCR pages', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} ocrPages={[]} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} ocrPages={[]} />)).not.toThrow();
    });

    it('should handle out-of-bounds current page', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} currentPage={5} />
      );

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} currentPage={5} />)).not.toThrow();

      // Test negative page index
      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} currentPage={-1} />)).not.toThrow();
    });
  });

  describe('Performance and memoization', () => {
    it('should not re-render unnecessarily when pageMetrics change', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} />
      );

      // Change pageMetrics to same values
      const sameMetrics = new Map([
        [0, { ...mockPageMetrics.get(0)! }],
      ]);

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} pageMetrics={sameMetrics} />)).not.toThrow();
    });

    it('should handle rapid pageMetrics updates', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} />
      );

      // Simulate rapid updates
      for (let i = 0; i < 10; i++) {
        const updatedMetrics = new Map([
          [0, {
            ...mockPageMetrics.get(0)!,
            offsetX: i * 10,
            offsetY: i * 5,
          }],
        ]);

        renderResult.rerender(<TextOverlay {...defaultProps} pageMetrics={updatedMetrics} />);
      }

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} />)).not.toThrow();
    });
  });

  describe('Visibility and interaction', () => {
    it('should not render when not visible', () => {
      const { UNSAFE_root } = render(
        <TextOverlay {...defaultProps} visible={false} />
      );

      // Should render nothing when not visible
      expect(UNSAFE_root.children).toHaveLength(0);
    });

    it('should render when visible with valid data', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} visible={true} />
      );

      // Should render content when visible
      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} visible={true} />)).not.toThrow();
    });

    it('should handle toggle visibility correctly', () => {
      const renderResult = render(
        <TextOverlay {...defaultProps} visible={true} />
      );

      renderResult.rerender(<TextOverlay {...defaultProps} visible={false} />);
      renderResult.rerender(<TextOverlay {...defaultProps} visible={true} />);

      expect(() => renderResult.rerender(<TextOverlay {...defaultProps} visible={true} />)).not.toThrow();
    });
  });

  describe('Multiple pages handling', () => {
    it('should handle multiple OCR pages correctly', () => {
      const multiplePages = [mockOCRPage, mockOCRPage, mockOCRPage];
      const multiplePageMetrics = new Map([
        [0, mockPageMetrics.get(0)!],
        [1, { ...mockPageMetrics.get(0)!, offsetY: 350 }],
        [2, { ...mockPageMetrics.get(0)!, offsetY: 700 }],
      ]);

      const renderResult = render(
        <TextOverlay
          {...defaultProps}
          ocrPages={multiplePages}
          pageMetrics={multiplePageMetrics}
          currentPage={1}
        />
      );

      expect(() => renderResult.rerender(<TextOverlay
        {...defaultProps}
        ocrPages={multiplePages}
        pageMetrics={multiplePageMetrics}
        currentPage={1}
      />)).not.toThrow();

      // Test switching between pages
      expect(() => renderResult.rerender(
        <TextOverlay
          {...defaultProps}
          ocrPages={multiplePages}
          pageMetrics={multiplePageMetrics}
          currentPage={2}
        />
      )).not.toThrow();
    });

    it('should use zero-based page indexing correctly', () => {
      // Test that when PDF reports currentPage: 1, overlay reads ocrPages[0]
      const firstPageOCR: OCRPage = {
        fullText: 'First page text',
        blocks: [{
          ...mockOCRBlock,
          text: 'First page content',
        }],
        width: 800,
        height: 600,
      };

      const secondPageOCR: OCRPage = {
        fullText: 'Second page text',
        blocks: [{
          ...mockOCRBlock,
          text: 'Second page content',
        }],
        width: 800,
        height: 600,
      };

      const multiplePages = [firstPageOCR, secondPageOCR];
      const multiplePageMetrics = new Map([
        [0, mockPageMetrics.get(0)!], // Zero-based indexing
        [1, { ...mockPageMetrics.get(0)!, offsetY: 350 }],
      ]);

      // When currentPage is 0 (zero-based), should show first OCR page
      const renderResult = render(
        <TextOverlay
          {...defaultProps}
          ocrPages={multiplePages}
          pageMetrics={multiplePageMetrics}
          currentPage={0}
        />
      );

      expect(() => renderResult.rerender(<TextOverlay
        {...defaultProps}
        ocrPages={multiplePages}
        pageMetrics={multiplePageMetrics}
        currentPage={0}
      />)).not.toThrow();

      // When currentPage is 1 (zero-based), should show second OCR page
      expect(() => renderResult.rerender(
        <TextOverlay
          {...defaultProps}
          ocrPages={multiplePages}
          pageMetrics={multiplePageMetrics}
          currentPage={1}
        />
      )).not.toThrow();
    });
  });

  describe('Pure coordinate transformation function', () => {
    it('should transform coordinates correctly at 0° rotation', () => {
      const result = mapBlockToOverlayRect(
        mockOCRBlock,
        mockOCRPage,
        mockPageMetrics.get(0)!,
        1,
        0
      );

      // Based on actual implementation: x = 100 * (400/800) + 50 offset = 100
      expect(result.x).toBeCloseTo(100); // 100 * (400/800) + 50 offset
      expect(result.y).toBeCloseTo(50); // 50 * (300/600) + 25 offset
      expect(result.width).toBeCloseTo(100); // 200 * (400/800)
      expect(result.height).toBeCloseTo(15); // 30 * (300/600)
    });

    it('should transform coordinates correctly at 90° rotation', () => {
      const pageMetricsWithRotation = {
        ...mockPageMetrics.get(0)!,
        offsetX: 100,
        offsetY: 50,
      };

      const result = mapBlockToOverlayRect(
        mockOCRBlock,
        mockOCRPage,
        pageMetricsWithRotation,
        1,
        90
      );

      // At 90° rotation, coordinates should be rotated around page center
      expect(Number.isFinite(result.x)).toBe(true);
      expect(Number.isFinite(result.y)).toBe(true);
      expect(result.width).toBeCloseTo(100);
      expect(result.height).toBeCloseTo(15);
    });

    it('should transform coordinates correctly at 180° rotation', () => {
      const result = mapBlockToOverlayRect(
        mockOCRBlock,
        mockOCRPage,
        mockPageMetrics.get(0)!,
        1,
        180
      );

      // At 180° rotation, coordinates should be rotated around page center
      expect(Number.isFinite(result.x)).toBe(true);
      expect(Number.isFinite(result.y)).toBe(true);
      expect(result.width).toBeCloseTo(100);
      expect(result.height).toBeCloseTo(15);
    });

    it('should transform coordinates correctly at 270° rotation', () => {
      const result = mapBlockToOverlayRect(
        mockOCRBlock,
        mockOCRPage,
        mockPageMetrics.get(0)!,
        1,
        270
      );

      // At 270° rotation, coordinates should be rotated around page center
      expect(Number.isFinite(result.x)).toBe(true);
      expect(Number.isFinite(result.y)).toBe(true);
      expect(result.width).toBeCloseTo(100);
      expect(result.height).toBeCloseTo(15);
    });

    it('should handle container layout compensation', () => {
      const containerLayout = { x: 10, y: 20, width: 400, height: 300 };

      const result = mapBlockToOverlayRect(
        mockOCRBlock,
        mockOCRPage,
        mockPageMetrics.get(0)!,
        1,
        0,
        containerLayout
      );

      // Should subtract container offsets
      // Based on actual implementation: x = 100 - 10 = 90
      expect(result.x).toBeCloseTo(90); // 100 - 10
      expect(result.y).toBeCloseTo(30); // 50 - 20
    });

    it('should return zero coordinates for invalid input', () => {
      const invalidBlock: OCRBlock = {
        ...mockOCRBlock,
        boundingBox: {
          left: NaN,
          top: NaN,
          width: NaN,
          height: NaN,
        },
      };

      const result = mapBlockToOverlayRect(
        invalidBlock,
        mockOCRPage,
        mockPageMetrics.get(0)!,
        1,
        0
      );

      expect(result).toEqual({ x: 0, y: 0, width: 0, height: 0 });
    });
  });
});
