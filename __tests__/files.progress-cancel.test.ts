import { createDocumentWithOcr, DocumentCreationProgress, CancellationToken, normalizeImageUri } from '@/utils/files';
import { performOcrOnImages } from '@/utils/ocr';
import * as FileSystem from 'expo-file-system';

// Mock dependencies
jest.mock('@/utils/ocr');
jest.mock('expo-file-system');
jest.mock('react-native-pdf-from-image', () => ({
  createPdf: jest.fn(),
}));

const mockPerformOcrOnImages = performOcrOnImages as jest.MockedFunction<typeof performOcrOnImages>;
const mockFileSystem = FileSystem as jest.Mocked<typeof FileSystem>;

// Mock PDF creation
const mockCreatePdf = require('react-native-pdf-from-image').createPdf;

describe('Files Utility Progress and Cancellation', () => {
  const mockImageUris = ['file://image1.jpg', 'file://image2.jpg', 'file://image3.jpg'];
  const mockPdfPath = '/path/to/document.pdf';

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockCreatePdf.mockResolvedValue(undefined);
    mockFileSystem.getInfoAsync.mockResolvedValue({
      exists: true,
      size: 1024000,
      isDirectory: false,
      uri: mockPdfPath,
      modificationTime: Date.now(),
    });
    mockFileSystem.deleteAsync.mockResolvedValue(undefined);
    mockFileSystem.documentDirectory = '/documents/';
    
    mockPerformOcrOnImages.mockResolvedValue({
      results: [
        { fullText: 'Page 1 text', blocks: [], width: 800, height: 600 },
        { fullText: 'Page 2 text', blocks: [], width: 800, height: 600 },
        { fullText: 'Page 3 text', blocks: [], width: 800, height: 600 },
      ],
      errors: [],
    });
  });

  describe('Progress reporting', () => {
    it('should call progress callback at correct intervals', async () => {
      const progressCallback = jest.fn();
      
      await createDocumentWithOcr(mockImageUris, {
        onProgress: progressCallback,
      });

      // Should call progress callback multiple times (8 calls due to additional progress reporting)
      expect(progressCallback).toHaveBeenCalledTimes(8);
      
      // Check initial progress
      expect(progressCallback).toHaveBeenNthCalledWith(1, {
        step: 'Initializing document creation',
        percentage: 0,
      });

      // Check PDF generation progress
      expect(progressCallback).toHaveBeenNthCalledWith(2, {
        step: 'Generating PDF from images',
        percentage: 10,
      });

      // Check completion progress
      expect(progressCallback).toHaveBeenLastCalledWith({
        step: 'Document creation complete',
        percentage: 100,
      });
    });

    it('should report OCR progress when not skipped', async () => {
      const progressCallback = jest.fn();
      
      // Mock OCR progress callback
      mockPerformOcrOnImages.mockImplementation(async (uris, options) => {
        // Simulate progress updates
        if (options?.onProgress) {
          options.onProgress({ completed: 0, total: 3 });
          options.onProgress({ completed: 1, total: 3 });
          options.onProgress({ completed: 2, total: 3 });
          options.onProgress({ completed: 3, total: 3 });
        }
        return {
          results: [
            { fullText: 'Page 1', blocks: [], width: 800, height: 600 },
            { fullText: 'Page 2', blocks: [], width: 800, height: 600 },
            { fullText: 'Page 3', blocks: [], width: 800, height: 600 },
          ],
          errors: [],
        };
      });

      await createDocumentWithOcr(mockImageUris, {
        skipOcr: false,
        onProgress: progressCallback,
      });

      // Should include OCR progress updates
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          step: expect.stringContaining('Extracting text from page'),
          percentage: expect.any(Number),
          currentItem: expect.any(Number),
          totalItems: expect.any(Number),
        })
      );
    });

    it('should skip OCR progress when OCR is disabled', async () => {
      const progressCallback = jest.fn();
      
      await createDocumentWithOcr(mockImageUris, {
        skipOcr: true,
        onProgress: progressCallback,
      });

      // Should not call OCR-related progress
      const progressCalls = progressCallback.mock.calls;
      const ocrProgressCalls = progressCalls.filter(call => 
        call[0].step.includes('text') || call[0].step.includes('OCR')
      );
      
      expect(ocrProgressCalls).toHaveLength(0);
    });

    it('should handle progress callback errors gracefully', async () => {
      const faultyProgressCallback = jest.fn(() => {
        throw new Error('Progress callback error');
      });

      // Should not throw even if progress callback fails
      await expect(createDocumentWithOcr(mockImageUris, {
        onProgress: faultyProgressCallback,
      })).resolves.toBeDefined();
    });
  });

  describe('Cancellation support', () => {
    it('should cancel before PDF generation', async () => {
      const cancelToken: CancellationToken = { cancelled: true };

      await expect(createDocumentWithOcr(mockImageUris, {
        cancelToken,
      })).rejects.toThrow('Document creation was cancelled by user');
    });

    it('should cancel during OCR processing', async () => {
      const cancelToken: CancellationToken = { cancelled: false };
      
      mockPerformOcrOnImages.mockImplementation(async (uris, options) => {
        // Simulate cancellation during OCR
        if (options?.cancelToken) {
          options.cancelToken.cancelled = true;
        }
        throw new Error('OCR processing was cancelled by user');
      });

      await expect(createDocumentWithOcr(mockImageUris, {
        skipOcr: false,
        cancelToken,
      })).rejects.toThrow('OCR processing was cancelled by user');
    });

    it('should clean up PDF file when cancelled', async () => {
      const cancelToken: CancellationToken = { cancelled: false };
      
      // Mock PDF creation success but then cancel
      mockCreatePdf.mockImplementation(async () => {
        cancelToken.cancelled = true;
        return undefined;
      });

      try {
        await createDocumentWithOcr(mockImageUris, {
          cancelToken,
        });
      } catch (error) {
        // Should attempt to clean up the PDF file
        expect(mockFileSystem.deleteAsync).toHaveBeenCalledWith(
          expect.stringContaining('.pdf'),
          { idempotent: true }
        );
      }
    });

    it('should handle cleanup errors gracefully', async () => {
      const cancelToken: CancellationToken = { cancelled: false };

      // Mock cleanup failure
      mockFileSystem.deleteAsync.mockRejectedValue(new Error('Cleanup failed'));

      // Mock file exists check to return true so cleanup is attempted
      mockFileSystem.getInfoAsync.mockResolvedValue({ exists: true });

      // Cancel during OCR processing to trigger cleanup
      mockPerformOcrOnImages.mockImplementation(async () => {
        cancelToken.cancelled = true;
        throw new Error('Document creation was cancelled by user');
      });

      await expect(createDocumentWithOcr(mockImageUris, {
        cancelToken,
      })).rejects.toThrow('Document creation was cancelled by user');

      // Should still attempt cleanup despite error
      expect(mockFileSystem.deleteAsync).toHaveBeenCalled();
    });
  });

  describe('Error handling with progress and cancellation', () => {
    it('should report progress on OCR failure but continue with PDF', async () => {
      const progressCallback = jest.fn();
      
      mockPerformOcrOnImages.mockRejectedValue(new Error('OCR failed'));

      const result = await createDocumentWithOcr(mockImageUris, {
        skipOcr: false,
        onProgress: progressCallback,
      });

      // Should still create document without OCR
      expect(result).toBeDefined();
      expect(result.ocrProcessed).toBe(false);
      
      // Should report progress about OCR failure
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          step: 'Text extraction failed, continuing with PDF only',
          percentage: 80,
        })
      );
    });

    it('should distinguish between cancellation and other errors', async () => {
      const progressCallback = jest.fn();
      
      // Test regular error
      mockCreatePdf.mockRejectedValue(new Error('PDF generation failed'));

      await expect(createDocumentWithOcr(mockImageUris, {
        onProgress: progressCallback,
      })).rejects.toThrow('Failed to create document with OCR');

      // Should report error progress
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          step: 'Document creation failed',
          percentage: 0,
        })
      );
    });
  });

  describe('Backward compatibility', () => {
    it('should work without progress callback', async () => {
      const result = await createDocumentWithOcr(mockImageUris);
      
      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.uri).toBeDefined();
    });

    it('should work without cancellation token', async () => {
      const result = await createDocumentWithOcr(mockImageUris, {
        skipOcr: true,
      });
      
      expect(result).toBeDefined();
      expect(result.ocrProcessed).toBe(false);
    });

    it('should maintain existing function signature compatibility', async () => {
      // Test with minimal options (original signature)
      const result1 = await createDocumentWithOcr(mockImageUris, {
        documentName: 'test.pdf',
      });
      
      expect(result1).toBeDefined();

      // Test with new options
      const result2 = await createDocumentWithOcr(mockImageUris, {
        documentName: 'test.pdf',
        skipOcr: true,
        onProgress: jest.fn(),
        cancelToken: { cancelled: false },
      });
      
      expect(result2).toBeDefined();
    });
  });

  describe('Progress percentage calculations', () => {
    it('should report accurate progress percentages', async () => {
      const progressCallback = jest.fn();
      
      await createDocumentWithOcr(mockImageUris, {
        skipOcr: false,
        onProgress: progressCallback,
      });

      const progressCalls = progressCallback.mock.calls.map(call => call[0].percentage);
      
      // Progress should be non-decreasing
      for (let i = 1; i < progressCalls.length - 1; i++) {
        expect(progressCalls[i]).toBeGreaterThanOrEqual(progressCalls[i - 1]);
      }
      
      // Should start at 0 and end at 100
      expect(progressCalls[0]).toBe(0);
      expect(progressCalls[progressCalls.length - 1]).toBe(100);
    });

    it('should handle different progress ranges for OCR vs non-OCR', async () => {
      const progressCallbackWithOCR = jest.fn();
      const progressCallbackWithoutOCR = jest.fn();
      
      // Test with OCR
      await createDocumentWithOcr(mockImageUris, {
        skipOcr: false,
        onProgress: progressCallbackWithOCR,
      });
      
      // Test without OCR
      await createDocumentWithOcr(mockImageUris, {
        skipOcr: true,
        onProgress: progressCallbackWithoutOCR,
      });

      const withOCRProgress = progressCallbackWithOCR.mock.calls.map(call => call[0].percentage);
      const withoutOCRProgress = progressCallbackWithoutOCR.mock.calls.map(call => call[0].percentage);
      
      // Both should reach 100%
      expect(Math.max(...withOCRProgress)).toBe(100);
      expect(Math.max(...withoutOCRProgress)).toBe(100);
    });
  });

  describe('URI normalization', () => {
    it('should handle file:// URIs correctly', () => {
      const fileUri = 'file:///path/to/image.jpg';
      expect(normalizeImageUri(fileUri)).toBe(fileUri);
    });

    it('should handle data: URIs correctly', () => {
      const dataUri = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8A0XqoFwX5Ps1a2gUewjN4wB5HuDXC/wBdA=';
      expect(normalizeImageUri(dataUri)).toBe(dataUri);
    });

    it('should convert bare paths to file:// URIs', () => {
      const barePath = '/path/to/image.jpg';
      expect(normalizeImageUri(barePath)).toBe('file:///path/to/image.jpg');
    });

    it('should handle content:// URIs correctly', () => {
      const contentUri = 'content://media/external/images/media/123';
      expect(normalizeImageUri(contentUri)).toBe(contentUri);
    });

    it('should return empty string for empty input', () => {
      expect(normalizeImageUri('')).toBe('');
    });

    it('should return input unchanged for unknown formats', () => {
      const unknownUri = 'http://example.com/image.jpg';
      expect(normalizeImageUri(unknownUri)).toBe(unknownUri);
    });
  });
});
