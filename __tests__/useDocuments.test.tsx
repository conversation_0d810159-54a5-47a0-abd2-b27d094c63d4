import React from "react";
import { render } from "@testing-library/react-native";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { StorageProvider } from "@/providers/storage";
import { useDocuments, Document } from "@/hooks/useDocuments";
import { act } from "react-test-renderer";

function createWrapper() {
  const client = new QueryClient();
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <QueryClientProvider client={client}>
      <StorageProvider>{children}</StorageProvider>
    </QueryClientProvider>
  );
  return Wrapper;
}

describe("useDocuments hook", () => {
  test("initially returns empty documents array", async () => {
    let api: ReturnType<typeof useDocuments> | null = null;

    function TestComp() {
      api = useDocuments();
      return null;
    }

    const Wrapper = createWrapper();
    render(<TestComp />, { wrapper: Wrapper });

    expect(api).not.toBeNull();
    expect(api!.documents).toEqual([]);
  });

  test("addDocument then deleteDocument updates list", async () => {
    let api: ReturnType<typeof useDocuments> | null = null;

    function TestComp() {
      api = useDocuments();
      return null;
    }

    const Wrapper = createWrapper();
    const utils = render(<TestComp />, { wrapper: Wrapper });

    const doc: Document = {
      id: "1",
      name: "Sample.pdf",
      uri: "data:application/pdf;base64,AAA",
      size: 3,
      createdAt: new Date().toISOString(),
    };

    await act(async () => {
      await api!.addDocument(doc);
    });
    utils.rerender(<TestComp />);
    expect(api!.documents).toHaveLength(1);
    expect(api!.documents[0].name).toBe("Sample.pdf");

    await act(async () => {
      await api!.deleteDocument("1");
    });
    utils.rerender(<TestComp />);
    expect(api!.documents).toHaveLength(0);
  });
});
