import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useStorage } from "@/providers/storage";
import { OCRPage } from "@/utils/ocr";

export interface Document {
  id: string;
  name: string;
  uri: string;
  size: number;
  createdAt: string;
  isScanned?: boolean;
  pageCount?: number;
  originalImages?: string[];
  // OCR-related fields
  ocrText?: string;
  ocrPages?: OCRPage[];
  ocrProcessed?: boolean;
  ocrEdited?: boolean;
}

const DOCUMENTS_KEY = "pdf_documents";

export function validatePdfDocument(document: Document): void {
  if (!document?.id?.trim() || !document?.name?.trim()) {
    throw new Error("Invalid document data: missing id or name");
  }

  if (!document.name.toLowerCase().endsWith('.pdf')) {
    throw new Error("Document name must have .pdf extension");
  }

  const uri = document.uri?.trim();
  if (!uri) {
    throw new Error("Document URI is required");
  }

  const isFileUri = uri.startsWith('file://');
  const isDataUri = uri.startsWith('data:');
  const isBlobUri = uri.startsWith('blob:');
  const isHttpUri = /^https?:\/\//.test(uri);
  const isPdfDataUri = isDataUri && uri.startsWith('data:application/pdf');
  const isLegacyImageDataUri = isDataUri && uri.startsWith('data:image/');

  if (document.isScanned) {
    if (!(isFileUri || isPdfDataUri || isLegacyImageDataUri)) {
      throw new Error("Scanned documents must resolve to a persistent file:// path or PDF data URL");
    }
  } else if (!(isFileUri || isDataUri || isBlobUri || isHttpUri)) {
    throw new Error("Document URI must be a file path, data URL, blob URL, or HTTP(S) URL");
  }
}

export function useDocuments() {
  const queryClient = useQueryClient();
  const { getItem, setItem } = useStorage();

  const documentsQuery = useQuery({
    queryKey: ["documents"],
    queryFn: async (): Promise<Document[]> => {
      try {
        const stored = await getItem(DOCUMENTS_KEY);
        return stored ? JSON.parse(stored) : [];
      } catch (error) {
        console.error("Error loading documents:", error);
        return [];
      }
    },
  });

  const addDocumentMutation = useMutation({
    mutationFn: async (document: Document) => {
      // Validate PDF document format
      validatePdfDocument(document);

      const currentDocuments = documentsQuery.data || [];
      const updatedDocuments = [...currentDocuments, document];
      await setItem(DOCUMENTS_KEY, JSON.stringify(updatedDocuments));
      return updatedDocuments;
    },
    onSuccess: (updatedDocuments) => {
      if (Array.isArray(updatedDocuments)) {
        queryClient.setQueryData(["documents"], updatedDocuments);
      }
    },
    onError: (error) => {
      console.error("Error adding document:", error);
    },
  });

  const deleteDocumentMutation = useMutation({
    mutationFn: async (documentId: string) => {
      if (!documentId?.trim()) {
        throw new Error("Invalid document ID");
      }

      const currentDocuments = documentsQuery.data || [];
      const updatedDocuments = currentDocuments.filter(doc => doc.id !== documentId.trim());
      await setItem(DOCUMENTS_KEY, JSON.stringify(updatedDocuments));
      return updatedDocuments;
    },
    onSuccess: (updatedDocuments) => {
      if (Array.isArray(updatedDocuments)) {
        queryClient.setQueryData(["documents"], updatedDocuments);
      }
    },
  });

  const updateDocumentMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Document> }) => {
      if (!id?.trim()) {
        throw new Error("Invalid document ID");
      }

      const currentDocuments = documentsQuery.data || [];
      const documentIndex = currentDocuments.findIndex(doc => doc.id === id.trim());

      if (documentIndex === -1) {
        throw new Error("Document not found");
      }

      // Merge updates with existing document
      const updatedDocument = { ...currentDocuments[documentIndex], ...updates };

      // Validate the updated document to keep invariants
      try {
        validatePdfDocument(updatedDocument);
      } catch (validationError) {
        console.warn('[Document Update] Validation failed for updated document:', validationError);
        // For legacy documents, we might want to relax some rules
        // For now, we'll allow the update to proceed but log the warning
      }

      const updatedDocuments = [...currentDocuments];
      updatedDocuments[documentIndex] = updatedDocument;

      await setItem(DOCUMENTS_KEY, JSON.stringify(updatedDocuments));
      return updatedDocuments;
    },
    onSuccess: (updatedDocuments) => {
      if (Array.isArray(updatedDocuments)) {
        queryClient.setQueryData(["documents"], updatedDocuments);
      }
    },
    onError: (error) => {
      console.error("Error updating document:", error);
    },
  });

  return {
    documents: documentsQuery.data || [],
    isLoading: documentsQuery.isLoading,
    error: documentsQuery.error,
    refetch: documentsQuery.refetch,
    addDocument: addDocumentMutation.mutateAsync,
    deleteDocument: deleteDocumentMutation.mutateAsync,
    updateDocument: updateDocumentMutation.mutateAsync,
  };
}