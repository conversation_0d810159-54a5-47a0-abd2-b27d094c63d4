// Custom Metro config to treat vendored PDF.js scripts as static assets
// so Metro/React Native won't try to parse Node-specific imports inside them.
const { getDefaultConfig } = require('@expo/metro-config');

/** @type {import('metro-config').ConfigT} */
module.exports = (async () => {
  const config = await getDefaultConfig(__dirname);
  const { assetExts, sourceExts } = config.resolver;

  return {
    ...config,
    resolver: {
      ...config.resolver,
      // Add a custom extension for vendored pdf.js files
      assetExts: [...assetExts, 'pdfjs'],
      sourceExts,
    },
  };
})();

