# Welcome to your Rork app

## Project info

This is a native cross-platform mobile app created with [Rork](https://rork.com)

**Platform**: Native iOS & Android app, exportable to web
**Framework**: Expo Router + React Native

## How can I edit this code?

There are several ways of editing your native mobile application.

### **Use Rork**

Simply visit [rork.com](https://rork.com) and prompt to build your app with AI.

Changes made via Rork will be committed automatically to this GitHub repo.

Whenever you make a change in your local code editor and push it to GitHub, it will be also reflected in Rork.

### **Use your preferred code editor**

If you want to work locally using your own code editor, you can clone this repo and push changes. Pushed changes will also be reflected in Rork.

If you are new to coding and unsure which editor to use, we recommend Cursor. If you're familiar with terminals, try Claude Code.

The only requirement is having Node.js & Bun installed - [install Node.js with nvm](https://github.com/nvm-sh/nvm) and [install Bun](https://bun.sh/docs/installation)

Follow these steps:

```bash
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
bun i

# Step 4: Start the instant web preview of your Rork app in your browser, with auto-reloading of your changes
bun run start-web

# Step 5: Start iOS preview
# Option A (recommended):
bun run start  # then press "i" in the terminal to open iOS Simulator
# Option B (if supported by your environment):
bun run start -- --ios
```

### **Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

## What technologies are used for this project?

This project is built with the most popular native mobile cross-platform technical stack:

- **React Native** - Cross-platform native mobile development framework created by Meta and used for Instagram, Airbnb, and lots of top apps in the App Store
- **Expo** - Extension of React Native + platform used by Discord, Shopify, Coinbase, Telsa, Starlink, Eightsleep, and more
- **Expo Router** - File-based routing system for React Native with support for web, server functions and SSR
- **TypeScript** - Type-safe JavaScript
- **React Query** - Server state management
- **Lucide React Native** - Beautiful icons

## How can I test my app?

### **On your phone (Recommended)**

1. **iOS**: Download the [Rork app from the App Store](https://apps.apple.com/app/rork) or [Expo Go](https://apps.apple.com/app/expo-go/id982107779)
2. **Android**: Download the [Expo Go app from Google Play](https://play.google.com/store/apps/details?id=host.exp.exponent)
3. Run `bun run start` and scan the QR code from your development server

### **In your browser**

Run `bun start-web` to test in a web browser. Note: The browser preview is great for quick testing, but some native features may not be available.

### **iOS Simulator / Android Emulator**

You can test Rork apps in Expo Go or Rork iOS app. You don't need XCode or Android Studio for most features.

**When do you need Custom Development Builds?**

- Native authentication (Face ID, Touch ID, Apple Sign In)
- In-app purchases and subscriptions
- Push notifications
- Custom native modules

Learn more: [Expo Custom Development Builds Guide](https://docs.expo.dev/develop/development-builds/introduction/)

If you have XCode (iOS) or Android Studio installed:

```bash
# iOS Simulator
bun run start -- --ios

# Android Emulator
bun run start -- --android
```

## How can I deploy this project?

### **EAS CLI Setup and Build Process**

This project uses EAS CLI as a devDependency for consistent builds across development environments. The build scripts are configured to use Bun's package runner for optimal performance.

1. **EAS CLI is already included** as a devDependency - no global installation needed:

   ```bash
   # EAS CLI is available via bunx
   bunx eas --version
   ```

2. **Configure your project** (if not already configured):

   ```bash
   bunx eas build:configure
   ```

3. **Available build scripts**:

   ```bash
   # Development builds
   bun run build:dev:ios          # iOS development build
   bun run build:dev:android      # Android development build
   bun run build:dev:ios-sim      # iOS simulator build

   # Preview builds
   bun run build:preview:ios      # iOS preview build
   bun run build:preview:android  # Android preview build

   # Production builds
   bun run build:prod:ios         # iOS production build
   bun run build:prod:android     # Android production build
   ```

### **Publish to App Store (iOS)**

1. **Build for iOS**:

   ```bash
   bun run build:prod:ios
   ```

2. **Submit to App Store**:
   ```bash
   bunx eas submit --platform ios
   ```

For detailed instructions, visit [Expo's App Store deployment guide](https://docs.expo.dev/submit/ios/).

### **Publish to Google Play (Android)**

1. **Build for Android**:

   ```bash
   bun run build:prod:android
   ```

2. **Submit to Google Play**:
   ```bash
   bunx eas submit --platform android
   ```

For detailed instructions, visit [Expo's Google Play deployment guide](https://docs.expo.dev/submit/android/).

### **Publish as a Website**

Your React Native app can also run on the web:

1. **Build for web**:

   ```bash
   bunx eas build --platform web
   ```

2. **Deploy with EAS Hosting**:
   ```bash
   bunx eas hosting:configure
   bunx eas hosting:deploy
   ```

Alternative web deployment options:

- **Vercel**: Deploy directly from your GitHub repository
- **Netlify**: Connect your GitHub repo to Netlify for automatic deployments

## App Features

This AI PDF Assistant includes:

### **Core Features**
- **Cross-platform compatibility** - Works on iOS, Android, and Web
- **File-based routing** with Expo Router
- **Tab navigation** with customizable tabs
- **Modal screens** for overlays and dialogs
- **TypeScript support** for better development experience
- **Async storage** for local data persistence
- **Vector icons** with Lucide React Native

### **Document Scanning & PDF Features**
- **Advanced document scanning** with auto edge detection and perspective correction
- **Multi-page document support** with batch processing capabilities
- **High-quality PDF generation** from scanned images using A4 paper size
- **OCR text extraction** with ML Kit for searchable PDFs
- **Text overlay visualization** with confidence-based color coding
- **PDF viewer** with zoom, rotation, and navigation controls
- **Document management** with local storage and metadata

### **Enhanced Scanning Experience**
- **Progress reporting** during document processing with real-time updates
- **Cancellation support** for long-running operations
- **OCR skip option** for faster processing when text extraction isn't needed
- **User preference persistence** for OCR settings
- **Error handling** with graceful fallbacks and user-friendly messages
- **Performance optimizations** with coordinate mapping accuracy improvements

## Project Structure

```
├── app/                    # App screens (Expo Router)
│   ├── (tabs)/            # Tab navigation screens
│   │   ├── _layout.tsx    # Tab layout configuration
│   │   └── index.tsx      # Home tab screen
│   ├── _layout.tsx        # Root layout
│   ├── modal.tsx          # Modal screen example
│   └── +not-found.tsx     # 404 screen
├── assets/                # Static assets
│   └── images/           # App icons and images
├── constants/            # App constants and configuration
├── app.json             # Expo configuration
├── package.json         # Dependencies and scripts
└── tsconfig.json        # TypeScript configuration
```

## Custom Development Builds

For advanced native features, you'll need to create a Custom Development Build instead of using Expo Go.

### **When do you need a Custom Development Build?**

- **Native Authentication**: Face ID, Touch ID, Apple Sign In, Google Sign In
- **In-App Purchases**: App Store and Google Play subscriptions
- **Advanced Native Features**: Third-party SDKs, platform-specifc features (e.g. Widgets on iOS)
- **Background Processing**: Background tasks, location tracking

### **Creating a Custom Development Build**

```bash
# EAS CLI is included as devDependency - no global installation needed

# Configure your project for development builds
bunx eas build:configure

# Create a development build for your device using the build scripts
bun run build:dev:ios        # iOS development build
bun run build:dev:android    # Android development build
bun run build:dev:ios-sim    # iOS simulator build

# Install the development build on your device and start developing
bun start --dev-client
```

**Learn more:**

- [Development Builds Introduction](https://docs.expo.dev/develop/development-builds/introduction/)
- [Creating Development Builds](https://docs.expo.dev/develop/development-builds/create-a-build/)
- [Installing Development Builds](https://docs.expo.dev/develop/development-builds/installation/)

## Document Scanning Features

### **How to Use the Scanner**

1. **Camera Scanner**: Use the advanced document scanner with auto edge detection
   - Supports multi-page documents (up to 10 pages)
   - Automatic perspective correction and image enhancement
   - Real-time progress reporting during processing

2. **Photo Library**: Select existing images from your device
   - Supports multiple image selection
   - Converts images to PDF format
   - Optional OCR text extraction

### **OCR Text Extraction**

The app includes ML Kit-powered OCR for text recognition:

- **Automatic text extraction** from scanned documents
- **Confidence-based highlighting** with color-coded text blocks
- **Text overlay visualization** in the PDF viewer
- **Searchable PDF content** when OCR is enabled
- **Skip OCR option** for faster processing when text extraction isn't needed

### **Performance Considerations**

- **Progress reporting** shows real-time status during document processing
- **Cancellation support** allows users to stop long-running operations
- **Optimized coordinate mapping** for accurate text overlay positioning
- **Efficient memory usage** with proper cleanup of temporary files
- **Throttled UI updates** for smooth performance during processing

### **Android-Specific Features**

- **Optimized permissions** - Uses modern scoped storage APIs
- **Required permissions**:
  - `CAMERA` - For document scanning
  - `READ_EXTERNAL_STORAGE` - For accessing photo library
  - `RECORD_AUDIO` - For camera functionality
  - `INTERNET` - For app functionality
- **No deprecated permissions** - Removed `WRITE_EXTERNAL_STORAGE` for Android 10+ compatibility

### **Testing the Scanning Functionality**

```bash
# Run unit tests for coordinate mapping
bun test TextOverlay.coordinate-mapping.test.tsx

# Run tests for progress and cancellation features
bun test files.progress-cancel.test.ts

# Run integration tests for scan screen
bun test ScanScreen.integration.test.tsx

# Run all tests
bun test
```

## Advanced Features

### **Add a Database**

Integrate with backend services:

- **Supabase** - PostgreSQL database with real-time features
- **Firebase** - Google's mobile development platform
- **Custom API** - Connect to your own backend

### **Add Authentication**

Implement user authentication:

**Basic Authentication (works in Expo Go):**

- **Expo AuthSession** - OAuth providers (Google, Facebook, Apple) - [Guide](https://docs.expo.dev/guides/authentication/)
- **Supabase Auth** - Email/password and social login - [Integration Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-expo-react-native)
- **Firebase Auth** - Comprehensive authentication solution - [Setup Guide](https://docs.expo.dev/guides/using-firebase/)

**Native Authentication (requires Custom Development Build):**

- **Apple Sign In** - Native Apple authentication - [Implementation Guide](https://docs.expo.dev/versions/latest/sdk/apple-authentication/)
- **Google Sign In** - Native Google authentication - [Setup Guide](https://docs.expo.dev/guides/google-authentication/)

### **Add Push Notifications**

Send notifications to your users:

- **Expo Notifications** - Cross-platform push notifications
- **Firebase Cloud Messaging** - Advanced notification features

### **Add Payments**

Monetize your app:

**Web & Credit Card Payments (works in Expo Go):**

- **Stripe** - Credit card payments and subscriptions - [Expo + Stripe Guide](https://docs.expo.dev/guides/using-stripe/)
- **PayPal** - PayPal payments integration - [Setup Guide](https://developer.paypal.com/docs/checkout/mobile/react-native/)

**Native In-App Purchases (requires Custom Development Build):**

- **RevenueCat** - Cross-platform in-app purchases and subscriptions - [Expo Integration Guide](https://www.revenuecat.com/docs/expo)
- **Expo In-App Purchases** - Direct App Store/Google Play integration - [Implementation Guide](https://docs.expo.dev/versions/latest/sdk/in-app-purchases/)

**Paywall Optimization:**

- **Superwall** - Paywall A/B testing and optimization - [React Native SDK](https://docs.superwall.com/docs/react-native)
- **Adapty** - Mobile subscription analytics and paywalls - [Expo Integration](https://docs.adapty.io/docs/expo)

## I want to use a custom domain - is that possible?

For web deployments, you can use custom domains with:

- **EAS Hosting** - Custom domains available on paid plans
- **Netlify** - Free custom domain support
- **Vercel** - Custom domains with automatic SSL

For mobile apps, you'll configure your app's deep linking scheme in `app.json`.

## Troubleshooting

### **App not loading on device?**

1. Make sure your phone and computer are on the same WiFi network
2. Try using tunnel mode: `bun start -- --tunnel`
3. Check if your firewall is blocking the connection

### **Build failing?**

1. Clear your cache: `bunx expo start --clear`
2. Delete `node_modules` and reinstall: `rm -rf node_modules && bun install`
3. Check [Expo's troubleshooting guide](https://docs.expo.dev/troubleshooting/build-errors/)

### **EAS Build Issues?**

1. Ensure you're using the project's EAS CLI: `bunx eas --version`
2. Use the provided build scripts: `bun run build:dev:android`
3. Check your EAS configuration in `eas.json`
4. Verify Android permissions are correctly configured in `app.json`

### **Scanning not working?**

1. **On Android**: Ensure camera permissions are granted
2. **OCR issues**: Check if ML Kit OCR is properly initialized
3. **Performance issues**: Try enabling OCR skip option for faster processing
4. **Coordinate mapping**: Text overlay should align properly with PDF content

### **Need help with native features?**

- Check [Expo's documentation](https://docs.expo.dev/) for native APIs
- Browse [React Native's documentation](https://reactnative.dev/docs/getting-started) for core components
- Visit [Rork's FAQ](https://rork.com/faq) for platform-specific questions

## About Rork

Rork builds fully native mobile apps using React Native and Expo - the same technology stack used by Discord, Shopify, Coinbase, Instagram, and nearly 30% of the top 100 apps on the App Store.

Your Rork app is production-ready and can be published to both the App Store and Google Play Store. You can also export your app to run on the web, making it truly cross-platform.
