---
type: "agent_requested"
description: "copilot instructions"
---

# Copilot Instructions for Rork AI PDF Assistant

## Project Overview
- **Framework:** Expo Router + React Native (cross-platform: iOS, Android, Web)
- **App Structure:**
  - `app/` contains all screens and navigation layouts
    - Tabs: `app/(tabs)/` with subfolders for `chat`, `documents`, `scan`
    - Each feature has its own folder and layout/index files
  - `hooks/` for custom React hooks (e.g., `useDocuments.ts`)
  - `providers/` for shared context/providers (e.g., `storage.tsx`)
  - `assets/` for images and static resources

## Developer Workflow
- **Install dependencies:** `bun i`
- **Web preview:** `bun run start-web`
- **Native preview:** `bun run start` (press `i` for iOS Simulator)
- **Alternative iOS:** `bun run start -- --ios`
- **All commands use Bun (not npm/yarn)**

## Key Patterns & Conventions
- **Navigation:** Uses Expo Router layouts (`_layout.tsx`) for screen grouping and navigation logic
- **Feature Isolation:** Each major feature (chat, documents, scan) is isolated in its own folder under `app/(tabs)/`
- **Custom Hooks:** Shared logic is placed in `hooks/` (see `useDocuments.ts` for document management)
- **Providers:** Shared state/context lives in `providers/` (see `storage.tsx`)
- **Assets:** Images are referenced from `assets/images/`
- **TypeScript:** All code is TypeScript-first; follow strict typing

## Integration Points
- **Rork Platform:** Changes pushed to GitHub are reflected in Rork (and vice versa)
- **Expo Router:** Handles navigation and screen organization
- **Bun:** Used for all package management and scripts

## Examples
- To add a new tab, create a folder in `app/(tabs)/` and add `_layout.tsx` and `index.tsx`
- To add a new hook, place it in `hooks/` and import where needed
- To add a new provider, place it in `providers/` and wrap your app in it via the main layout

## Tips for AI Agents
- Always use Bun for scripts and dependency management
- Respect the folder structure for features, hooks, and providers
- Reference images from `assets/images/`
- Use TypeScript and Expo Router conventions for navigation
- Document new patterns in this file if they differ from above

---
_Last updated: September 21, 2025_
