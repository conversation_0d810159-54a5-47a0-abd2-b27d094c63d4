I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The codebase currently uses a WebView + PDF.js implementation for PDF viewing with complex HTML generation and message passing. The app has comprehensive features including OCR text overlay, document scanning, PDF generation from images, chat functionality, and sharing. The current implementation works but has performance and stability issues typical of WebView-based solutions. The migration to a native PDF viewer is needed for better performance and stability while preserving all existing functionality.

### Approach

**Migration Strategy: Native PDF Viewer (Android/iOS only)**

We'll migrate to a native PDF viewer using `react-native-pdf` on Android and iOS. Web is out of scope for this plan and will not be implemented.

**Key Components:**
- Create a new `NativePDF.tsx` component wrapping `react-native-pdf`
- Refactor `PDFViewer.tsx` into a platform-aware facade
- Maintain identical public API to minimize breaking changes
- Preserve all OCR overlay functionality with coordinate mapping
- Update build configuration for native dependencies
- Comprehensive testing and rollback capability

### Reasoning

I analyzed the current codebase structure and found a WebView-based PDF viewer using PDF.js with vendored assets. I examined the related features including OCR text overlay, document scanning workflow, file utilities, and test coverage. I researched available native PDF viewer options and determined that `react-native-pdf` with Expo config plugins is the best solution for Android and iOS. Web support is explicitly excluded from this migration.

## Mermaid Diagram

sequenceDiagram
    participant App as App/(tabs)/documents/viewer.tsx
    participant PDFViewer as PDFViewer.tsx (Facade)
    participant NativePDF as NativePDF.tsx
    participant TextOverlay as TextOverlay.tsx
    participant RNPdf as react-native-pdf

    App->>PDFViewer: render with source & props
    PDFViewer->>NativePDF: render native implementation (Android/iOS)
    NativePDF->>RNPdf: render Pdf component
    RNPdf->>NativePDF: onLoadComplete, onPageChanged, onScaleChanged
    NativePDF->>PDFViewer: onMetrics (pageMetrics)
    PDFViewer->>TextOverlay: pass pageMetrics & OCR data
    TextOverlay->>TextOverlay: mapBlockToOverlayRect

    App->>PDFViewer: call ref.zoomIn()
    PDFViewer->>NativePDF: forward zoom command
    NativePDF->>RNPdf: update scale prop
    RNPdf->>NativePDF: onScaleChanged
    NativePDF->>PDFViewer: updated metrics
    PDFViewer->>TextOverlay: updated pageMetrics

## Proposed File Changes

### package.json(MODIFY)

Add native PDF viewer dependencies (Android/iOS only):
- `react-native-pdf` version 6.7.5 (compatible with Expo SDK 52)
- `react-native-blob-util` for file handling support
- `@config-plugins/react-native-pdf` version 8.0.0 for Expo integration
- `@config-plugins/react-native-blob-util` for blob utilities

These versions are specifically chosen to match the Expo SDK 52 compatibility matrix as referenced in the Expo config-plugins documentation.

### app.json(MODIFY)

Add Expo config plugins to the plugins array (mobile only):
- `["@config-plugins/react-native-pdf"]` for native PDF rendering support
- `["@config-plugins/react-native-blob-util"]` for file operations

These plugins will configure the native Android and iOS projects to include the necessary native modules and permissions for PDF rendering. The plugins handle all the native configuration automatically during the prebuild process.

### components/pdf/NativePDF.tsx(NEW)

References: 

- components/pdf/PDFViewer.tsx(MODIFY)
- components/pdf/TextOverlay.tsx(MODIFY)

Create a new native PDF viewer component that wraps `react-native-pdf`:

**Core Functionality:**
- Import and use the `Pdf` component from `react-native-pdf`
- Accept the same props interface as the existing `PDFViewer.tsx` for seamless integration
- Implement ref methods: `zoomIn()`, `zoomOut()`, `rotate()`, `fitWidth()`
- Handle PDF source conversion from base64 to blob URI format required by react-native-pdf

**State Management:**
- Track current page, zoom level, rotation state internally
- Manage page metrics for OCR overlay coordinate mapping
- Handle loading and error states with appropriate UI feedback

**Event Handling:**
- Use `onLoadComplete` to get total page count and initial setup
- Use `onPageChanged` to track current visible page
- Use `onScaleChanged` to track zoom level changes
- Generate page metrics compatible with `TextOverlay.tsx` requirements

**Page Metrics Generation:**
- Calculate page dimensions and positions for OCR overlay
- Map react-native-pdf's coordinate system to TextOverlay expectations
- Handle rotation transformations for coordinate mapping

**Error Handling:**
- Graceful fallback to sharing/external app opening on render failures
- Comprehensive error logging for debugging

This component will be platform-specific and only used on iOS/Android.

### components/pdf/PDFViewer.tsx(MODIFY)

References: 

- components/pdf/NativePDF.tsx(NEW)

Refactor the existing PDFViewer into a platform-aware facade component for Android/iOS:

**Platform Detection:**
- Add `Platform.OS` check targeting only Android/iOS
- For iOS/Android with PDF source: use new `NativePDF.tsx` component
- For web: out of scope; render a lightweight unsupported placeholder or no-op
- For image sources: keep existing image handling logic on mobile

**Component Selection Logic:**
```
if (Platform.OS === 'ios' || Platform.OS === 'android') {
  // Use NativePDF component for PDFs
} else {
  // Web/other: out of scope (render placeholder or return null)
}
```

**API Preservation:**
- Maintain identical prop interface and ref methods
- Ensure all existing functionality works seamlessly
- Forward all props and callbacks to the appropriate underlying component

**Ref Forwarding:**
- Create a unified ref that forwards commands to NativePDF
- Ensure `zoomIn`, `zoomOut`, `rotate`, `fitWidth` work consistently on Android/iOS

**Backward Compatibility:**
- Maintain support for both PDF and image viewing modes on Android/iOS
- Preserve all callback signatures and event handling
- Web is explicitly out of scope for this migration

This ensures zero breaking changes for consuming components like `viewer.tsx`.

### components/pdf/TextOverlay.tsx(MODIFY)

References: 

- components/pdf/NativePDF.tsx(NEW)

Update the TextOverlay component to handle page metrics from the native PDF viewer:

**Enhanced Coordinate Mapping:**
- Update `mapBlockToOverlayRect` function to handle different metric sources
- Add fallback logic for missing `naturalWidth`/`naturalHeight` from native PDF viewer
- Improve error handling for coordinate transformation edge cases

**Page Metrics Compatibility:**
- Handle metrics from react-native-pdf which may have different structure
- Add validation for required metric fields with sensible defaults
- Ensure coordinate mapping works correctly with native PDF's layout system

**Rotation Handling:**
- Verify rotation transformations work with native PDF viewer's coordinate system
- Add platform-specific adjustments if needed for coordinate accuracy

**Performance Optimizations:**
- Enhance memoization for page metrics to prevent unnecessary re-renders
- Optimize coordinate calculations for better performance with native viewer

**Testing Support:**
- Ensure the exported `mapBlockToOverlayRect` function works with both metric formats
- Add defensive programming for undefined or malformed page metrics

The goal is to maintain pixel-perfect OCR overlay accuracy regardless of the underlying PDF viewer implementation.

### utils/files.ts(MODIFY)

References: 

- components/pdf/NativePDF.tsx(NEW)

Add utility functions to support native PDF viewer integration:

**Base64 to Blob Conversion:**
- Add `convertBase64ToBlobUri()` function for react-native-pdf compatibility
- Handle platform-specific blob creation for iOS/Android
- Manage temporary file cleanup for blob URIs

**PDF Source Preparation:**
- Add `preparePdfSourceForNative()` function to convert various URI formats
- Handle data URLs, file URIs, and blob URIs consistently
- Ensure proper caching and cleanup of temporary files

**File Size Validation:**
- Enhance `isLargeFile()` with platform-specific thresholds
- Add memory usage considerations for native PDF rendering
- Provide warnings for files that might cause performance issues

**Error Handling:**
- Add comprehensive error handling for file conversion operations
- Provide fallback mechanisms for unsupported file formats
- Log detailed error information for debugging

These utilities will bridge the gap between the app's current file handling and react-native-pdf's requirements.

### __mocks__/react-native-pdf.js(NEW)

References: 

- __mocks__/react-native-document-scanner-plugin.js

Create Jest mock for react-native-pdf to support unit testing on mobile paths:

**Mock Implementation:**
- Export a mock `Pdf` component that renders as a simple `View`
- Mock all props and callbacks with jest.fn() implementations
- Simulate basic PDF loading and page change events for testing

**Test Support:**
- Provide controllable mock responses for different test scenarios
- Allow tests to simulate loading, error, and success states
- Enable testing of component integration without native dependencies

**Mock Structure:**
```javascript
module.exports = {
  default: 'Pdf', // For default import
  Pdf: 'Pdf'      // For named import
};
```

This ensures all existing tests continue to pass and new tests can be written for the native PDF functionality.

### __tests__/NativePDF.test.tsx(NEW)

References: 

- __tests__/PDFViewer.renders.test.tsx(MODIFY)
- __mocks__/react-native-pdf.js(NEW)

Create comprehensive test suite for the new NativePDF component:

**Component Rendering Tests:**
- Test that NativePDF renders without crashing
- Verify proper prop forwarding to underlying Pdf component
- Test loading and error state UI rendering

**Ref Method Tests:**
- Test `zoomIn()`, `zoomOut()`, `rotate()`, `fitWidth()` ref methods
- Verify state updates when ref methods are called
- Test method chaining and edge cases

**Event Handling Tests:**
- Mock and test `onLoadComplete`, `onPageChanged`, `onScaleChanged` callbacks
- Verify page metrics generation and format
- Test error handling and fallback scenarios

**Integration Tests:**
- Test integration with TextOverlay component
- Verify coordinate mapping accuracy with mock page metrics
- Test platform-specific behavior and fallbacks

**Mock Usage:**
- Utilize the new react-native-pdf mock for isolated testing
- Test component behavior without native dependencies
- Verify proper cleanup and memory management

These tests ensure the native PDF viewer works correctly and maintains compatibility with existing features.

### __tests__/PDFViewer.renders.test.tsx(MODIFY)

References: 

- __tests__/NativePDF.test.tsx(NEW)
- __mocks__/react-native-pdf.js(NEW)

Update existing PDFViewer tests to handle the new Android/iOS implementation:

**Platform Mocking:**
- Add Platform.OS mocking for iOS and Android
- Test that the native implementation is rendered on mobile
- Verify prop forwarding works correctly for the native implementation

**Component Selection Tests:**
- Test iOS/Android platforms use NativePDF implementation
- Optionally, verify web renders an unsupported placeholder (out of scope)
- Test image sources continue to work on mobile

**Backward Compatibility Tests:**
- Ensure all existing test cases continue to pass
- Verify no breaking changes in component API
- Test that ref methods work consistently across platforms

**Mock Integration:**
- Use mocks to handle react-native-pdf components
- Ensure test isolation and proper cleanup
- Add platform-specific test scenarios for Android/iOS

The goal is to maintain 100% test coverage while adding comprehensive testing for the new native PDF functionality.

### docs/plan/native-pdf-migration.md(NEW)

References: 

- docs/plan/250922.pdf.js.md

Create comprehensive documentation for the native PDF viewer migration:

**Migration Overview:**
- Document the reasons for migration from WebView + PDF.js to native viewer
- Clarify scope: Android/iOS only; web excluded
- List performance and stability benefits

**Technical Implementation:**
- Document the new component architecture and platform detection logic
- Explain how page metrics are generated and mapped for OCR overlay
- Detail the coordinate transformation system for TextOverlay compatibility

**Dependencies and Configuration:**
- List all new dependencies and their versions
- Document Expo config plugin setup and requirements
- Explain EAS build configuration changes if any

**Feature Compatibility:**
- Document which features work on Android/iOS
- List any platform-specific limitations or differences
- Explain fallback mechanisms and error handling

**Testing Strategy:**
- Document test coverage for new components
- List manual testing scenarios and QA checklist
- Explain mock setup and testing approach

**Rollback Plan:**
- Document how to revert to the previous WebView-based implementation on mobile if needed
- List potential issues and troubleshooting steps
- Provide performance monitoring guidelines

**Future Considerations:**
- Document potential migration to commercial PDF SDKs (PSPDFKit, Apryse)
- List areas for future optimization and enhancement
- Explain maintenance and update procedures

This documentation will serve as a reference for the team and future maintenance.

### docs/codebase.md(MODIFY)

References: 

- docs/plan/native-pdf-migration.md(NEW)

Update the codebase documentation to reflect the new Android/iOS PDF viewer architecture:

**PDF Viewer Section Update:**
- Document the native PDF viewer approach for Android/iOS
- Remove web considerations from the scope
- Update component hierarchy and data flow diagrams

**Architecture Changes:**
- Document the new `NativePDF.tsx` component and its role
- Explain the facade pattern used in `PDFViewer.tsx`
- Update dependency graphs and integration points

**Feature Documentation:**
- Update OCR overlay documentation to reflect coordinate mapping changes
- Document platform-specific capabilities and limitations
- Explain fallback mechanisms and error handling

**Development Guidelines:**
- Add guidelines for testing PDF functionality on Android/iOS
- Document best practices for PDF handling and performance
- Update troubleshooting section with native PDF specific issues

**Dependencies Section:**
- Add documentation for react-native-pdf and related dependencies
- Explain Expo config plugin requirements and setup
- Update build and deployment considerations

This ensures the codebase documentation stays current and helpful for new team members.

### Robustness & Guardrails (Addendum)

Strengthen the migration with the following cross-cutting improvements. These do not change scope (Android/iOS only) but reduce risk and clarify implementation details.

**Risks & Mitigations**
- File/memory pressure: Prefer `file://` URIs via `expo-file-system` over base64; hard size/page-count thresholds; optional “Open in external app” fallback.
- Gesture conflicts: Ensure pinch/zoom/scroll don’t conflict with parent navigators; configure `react-native-gesture-handler` appropriately.
- Error containment: Wrap viewer screen with an error boundary; standardize error codes (load-failure, password-needed, timeout, OOM) and user-facing messages with retry.
- Dependency drift: Pin exact versions for `react-native-pdf` and config plugins; document a known-good matrix for Expo SDK 52.

**Architecture Enhancements**
- Facade stability: Keep `PDFViewer.tsx` as minimal facade to allow library swaps without touching callers.
- Feature flag: Add a build-time or remote-config flag to disable native viewer quickly and offer external-app fallback.
- Password-protected PDFs: Extend props to accept optional `password` and add a basic UI prompt when required.

**Caching & File Handling**
- Pipeline: Base64/data → write to cache (`FileSystem.cacheDirectory`) → pass `file://` to `react-native-pdf`.
- LRU cache: Introduce content-hash keyed cache entries with a max size; evict oldest on overflow; cleanup on unmount and app startup.
- Thresholds: Soft warn at ~50MB/800 pages; hard block at ~100MB/2000 pages (configurable).

**Page Metrics & Overlay Accuracy**
- Canonical type: Define a strict TS type for page metrics (index, naturalWidth/Height, renderedWidth/Height, scale, rotation, spacing, insets).
- Per-page sizes: Do not assume uniform page size; compute per page metrics when available.
- Transform math: Implement explicit scale→rotate→translate matrix helpers with unit tests; centralize rounding to avoid sub-pixel drift.

**UX & Layout**
- Fit modes: Support `fitWidth`, `fitHeight`, `fitPage`; persist last-used mode per document.
- Pagination policy: Prefer paged mode for very large PDFs to reduce memory; document decision.
- Orientation: Validate overlays in landscape and split-screen.

**Error Handling & Timeouts**
- Timeouts: Add load timeout and cancel; surface retry and fallback actions.
- Structured logs: Include error code, file size, page count, device memory class for triage.

**Dependencies & Build Notes**
- iOS: Confirm minimum iOS version and any plugin Swift/`use_frameworks!` requirements.
- Android: Keep temp files in app cache; avoid external storage permissions; ensure scoped storage compliance.
- Prefer `expo-file-system`: Only include `react-native-blob-util` if strictly needed by the viewer code path.

**Testing & Observability**
- Unit tests: Transform helpers, metrics generation, ref methods (`zoomIn`, `zoomOut`, `fitWidth`, `rotate`), and error flows (password, timeout).
- Fixtures: Include small multi-page and rotated-page PDFs; snapshot computed metrics and overlay rects.
- E2E (Detox): Render, paginate, pinch-zoom, rotate; verify overlay alignment via element bounds.
- Device matrix: Low-memory Android (Go), Android 13/14, iOS 15–17; include landscape.
- Telemetry: Sample page render time, memory warnings, load failures; add analytics to detect regressions.

**Performance**
- Warm metrics: Precompute/cache metrics after first load; debounce `onScaleChanged` to limit rerenders.
- Throttle overlays: Recompute overlay rects at animation frames during pinches.
- Avoid heavy images in overlays; prefer lightweight views for highlights.

**Security & Privacy**
- Sensitive docs: Keep PDFs in cache; clear on logout and app upgrades.
- External links: Gate `onLinkPress` with confirmation and enterprise policy.

**Documentation Additions**
- Migration checklist: Install deps → prebuild → run iOS/Android → QA → thresholds → enable flag.
- Troubleshooting table: Blank screen, Android 14 issues, iOS memory warnings, password errors, misconfiguration fixes.
- Known limitations: Annotations not editable; no native text selection; behavior for very large PDFs.

**Open Questions**
- Do password-protected PDFs need first-class support at launch?
- Continuous scroll vs paged mode for large documents?
- Max supported size/page count and UX at limits?
- Is text selection required, or only overlay highlights?
