I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I analyzed the current Expo project and found it's using Expo SDK 53 with standard Expo Go setup. The project already has camera and image picker functionality configured, but lacks EAS Development Build configuration and PDF generation capabilities. The `react-native-pdf-from-image` library requires native code compilation, so switching to EAS Development Build is necessary. No `eas.json` file exists yet, and the current `app.json` needs updates for development build support.

### Approach

I'll configure EAS Development Build by creating `eas.json` with development, preview, and production profiles. Then I'll install `expo-dev-client` and `react-native-pdf-from-image` dependencies. The `app.json` will be updated to include the dev client plugin. Since `react-native-pdf-from-image` doesn't require a config plugin, I'll focus on ensuring proper EAS build configuration. Finally, I'll update the package.json scripts to support EAS builds and provide clear instructions for building and testing the development client.

### Reasoning

I examined the project structure and found the current `app.json` and `package.json` files. I researched the requirements for `react-native-pdf-from-image` and discovered it needs native compilation without requiring a config plugin. I also researched EAS Development Build setup requirements and found the standard configuration patterns for `eas.json` and the necessary dependencies.

## Mermaid Diagram

sequenceDiagram
    participant Dev as Developer
    participant EAS as EAS Build Service
    participant Device as Test Device
    participant App as PDF App

    Dev->>EAS: eas build --profile development
    EAS->>EAS: Install expo-dev-client
    EAS->>EAS: Install react-native-pdf-from-image
    EAS->>EAS: Compile native code
    EAS->>Device: Download & install dev client
    Dev->>App: expo start (development server)
    Device->>App: Connect to dev server
    App->>App: Test PDF generation from images

## Proposed File Changes

### eas.json(NEW)

Create the EAS build configuration file with three profiles:

- **development**: Configure for development builds with `developmentClient: true` and `distribution: "internal"` for testing on devices/simulators
- **development-simulator**: Add iOS simulator-specific profile with `ios.simulator: true` for testing on iOS Simulator
- **preview**: Configure for internal testing builds without dev tools, using APK format for Android for easier distribution
- **production**: Configure for production app store builds

Set Node.js version to ensure consistent builds across environments. Configure resource classes and enable clear build output for better debugging.

### app.json(MODIFY)

Update the Expo configuration to support EAS Development Build:

- Add `expo-dev-client` to the plugins array to enable development client functionality
- Ensure the existing plugins (`expo-router`, `expo-document-picker`, `expo-image-picker`, `expo-camera`) remain configured properly
- Verify that permissions and bundle identifiers are correctly set for both iOS and Android platforms
- Maintain the existing `newArchEnabled: true` setting for React Native's new architecture support

The configuration should support both Expo Go (for basic development) and EAS Development Build (for native modules like PDF generation).

### package.json(MODIFY)

Install required dependencies and update build scripts:

**Dependencies to add:**
- `expo-dev-client`: Required for EAS Development Build functionality
- `react-native-pdf-from-image`: Core library for generating PDFs from images

**Scripts to add:**
- `build:dev:ios`: Build development client for iOS devices
- `build:dev:android`: Build development client for Android devices
- `build:dev:ios-sim`: Build development client for iOS Simulator
- `build:preview:ios`: Build preview version for iOS internal testing
- `build:preview:android`: Build preview version for Android internal testing
- `build:prod:ios`: Build production version for iOS App Store
- `build:prod:android`: Build production version for Google Play Store

Update the existing scripts to work with EAS builds while maintaining the current development workflow with the custom `bunx rork start` commands.