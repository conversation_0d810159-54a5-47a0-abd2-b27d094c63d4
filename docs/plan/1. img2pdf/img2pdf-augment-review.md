## Executive summary

The img2pdf planning series (0–3) outlines a phased implementation: enabling EAS Dev Client and native dependencies (0), generating real PDFs from images (1), adding native scanning with edge detection (2), and integrating OCR with text overlay (3).

Current repository state indicates that most of the plan has already been implemented and is functioning:
- EAS configuration and dev client are present (eas.json, app.json plugins, package.json build scripts).
- PDF generation is implemented via react-native-pdf-from-image in utils/files.ts (with require-guard and temp-file handling).
- Native document scanning is integrated (react-native-document-scanner-plugin) and used in app/(tabs)/scan/index.tsx.
- OCR is implemented via react-native-mlkit-ocr with utilities (utils/ocr.ts), and a text overlay for the PDF viewer is present (components/pdf/TextOverlay.tsx) and wired into components/pdf/PDFViewer.tsx and app/(tabs)/documents/viewer.tsx.

Key gaps and risks:
- Web parity is missing for PDF generation/OCR paths; createDocumentWithOcr will fail on web because native modules are unavailable.
- Overlay coordinate mapping is likely incorrect under zoom/rotation and varies by page; alignment issues probable.
- Package management/scripts: EAS CLI is used in scripts but not declared as a dev dependency; guidance says “All commands use Bun.”
- Android permissions include legacy storage permissions; consider modern scoped storage and READ_MEDIA_* updates.
- Performance/UX: long-running OCR/PDF tasks lack cancel/progress granularity; concurrency is fixed.

This review refines the plan with concrete, project-aligned improvements, adds risk mitigations, and proposes a focused test strategy consistent with the repo’s standards (.github/copilot-instructions.md and .github/ts-expo.md).

---

## Context and standards considered

- .github/copilot-instructions.md
  - Expo Router + React Native; Bun for all scripts; feature isolation in app/(tabs)/; TypeScript-first.
- .github/ts-expo.md
  - Functional components and interfaces; strict TS; performance practices; testing via Jest/RNTL and Detox; a11y; web considerations.

These imply:
- Prefer Bun-driven scripts (bunx) and local CLI deps.
- Keep navigation within Expo Router; avoid ad-hoc routes.
- Ensure cross-platform behavior; where native-only, provide web fallbacks.

---

## Implementation analysis (plan vs. code)

1) 0. EAS development build & dependencies
- Plan: Add eas.json, expo-dev-client, build scripts, app.json plugin setup.
- Code: DONE. eas.json present with development/preview/production, app.json includes expo-dev-client and scanner plugin, package.json has build:* scripts. Dependencies include expo-dev-client and all native libs.

2) 1. Real PDF generation from images
- Plan: Implement generatePdfFromImages and integrate in scan flows; update document model and error handling.
- Code: DONE in utils/files.ts (generatePdfFromImages + createDocumentWithOcr). Hooks/useDocuments.ts includes validation and update mutation. Scan screen uses the OCR-enabled creation path.

3) 2. Document scanning with edge detection
- Plan: Integrate react-native-document-scanner-plugin across scan flows.
- Code: DONE. app/(tabs)/scan/index.tsx uses DocumentScanner.scanDocument with multi-page support and proper user feedback.

4) 3. OCR + text overlay
- Plan: Integrate react-native-mlkit-ocr; extend model; add overlay; UI to view/edit text.
- Code: DONE. utils/ocr.ts implements per-image and batched OCR with concurrency/normalization; hooks/useDocuments.ts has ocr* fields and update; components/pdf/TextOverlay.tsx + PDFViewer overlay; viewer UI supports view/edit OCR text.

Conclusion: The plan largely reflects the current state; its execution is ahead of documentation. Remaining work should focus on hardening, parity (web), and DX.

---

## Gap analysis and potential challenges

1) Web parity for scan → PDF → OCR flow
- Issue: createDocumentWithOcr relies on native modules (react-native-pdf-from-image, react-native-mlkit-ocr). On web, require-guard throws (assertPdfModuleAvailable). app/(tabs)/scan/index.tsx calls createDocumentWithOcr for Photo Library on web.
- Impact: Web flow errors when selecting images; violates “Web preview” guidance.
- Mitigation: Provide web-only fallbacks:
  - PDF generation: pdf-lib (browser) to compose multi-page PDFs from images.
  - OCR: Tesseract.js (WebAssembly) with worker and progress callbacks.
  - Gate by Platform.OS === 'web' in utils/files.ts and utils/ocr.ts; expose unified API.

2) Text overlay coordinate mapping accuracy
- Observation: TextOverlay uses pageMetrics rects but scales x/y by pageWidth/pageHeight producing no-op; rotation math uses page center but may not match pdf.js viewport transforms; OCRPage.width/height often 0.
- Risk: Misaligned overlay boxes, especially under zoom/rotation or differing page sizes.
- Mitigation: 
  - Ensure OCRPage stores source image pixel width/height reliably.
  - In PDFViewer, send pdf.js viewport metrics per page (width/height at scale) and naturalSize per page.
  - Compute scaleX = renderedWidth / ocrPage.width and scaleY = renderedHeight / ocrPage.height; apply offset based on holder position.
  - Recompute on metrics events and debounce for performance.

3) Android permissions and storage
- app.json uses READ/WRITE_EXTERNAL_STORAGE (legacy; WRITE is deprecated on Android 10+). Expo 53 favors scoped storage.
- Mitigation: Remove WRITE_EXTERNAL_STORAGE; prefer MediaLibrary APIs when exporting to gallery; consider READ_MEDIA_IMAGES on Android 13+ if needed.

4) EAS CLI and Bun consistency
- Scripts call eas directly but project guidance prefers Bun; eas is not in devDependencies.
- Risk: Global CLI reliance; CI variance.
- Mitigation: Add eas-cli as devDependency and use bunx eas ... in scripts.

5) Long-running operations UX
- OCR and PDF generation may take seconds; current UI shows a single generic state.
- Mitigation: Add progress reporting and cancellation:
  - utils/ocr.ts: emit per-image progress via callback; plumb into Scan screen.
  - Allow skip OCR toggle for speed.

6) Memory/large files
- Viewer reads entire PDF as base64; large docs may stress memory.
- Mitigation: Consider streaming-friendly approaches or size gating (show warning over N MB); on web use iframe/object with data URL already used; on native consider file:// pipe with minimal base64 duplication.

7) Error mapping and resilience
- createDocumentWithOcr swallows OCR errors and proceeds, which is good; ensure user-facing messages distinguish PDF-only vs. with-text.
- Validate that try/finally paths clear isProcessing on every early return (try/finally ensures this today).

8) iOS ML Kit integration nuances
- MLKit adds large pods; verify no need for use_frameworks!; watch build times and app size. Keep resourceClass medium as configured.

---

## Technical feasibility assessment

- Expo SDK 53 + RN 0.79.1 is compatible with the chosen native modules in a Dev Client/EAS context.
- The current code adheres to project standards: TypeScript-first, Expo Router, feature isolation within app/(tabs)/, utilities in utils/, and hooks in hooks/.
- The main feasibility constraint is cross-platform behavior: native libs do not run on web; a web strategy is needed to satisfy “start-web” flows.

---

## Enhancement recommendations

A) Task breakdown (refined plan)

1. Web parity (High priority)
- Add web implementations:
  - utils/files.web.ts: generatePdfFromImagesWeb using pdf-lib; preserve API shape.
  - utils/ocr.web.ts: performOcrOnImagesWeb using Tesseract.js; concurrency via workers; same types.
  - Platform-select in barrel files or conditional imports to keep call sites unchanged.
- Update Scan screen to show web-specific messaging and progress.

2. Overlay correctness and performance (High)
- Extend PDFViewer to emit per-page viewport metrics and page natural sizes.
- Fix TextOverlay transformations (scale, translate, rotate) using those metrics.
- Add buildWhen-like memoization to limit re-renders on unrelated metric changes; throttle metrics messages from WebView.

3. Permissions and config hygiene (Medium)
- Remove deprecated Android WRITE_EXTERNAL_STORAGE; audit required permissions with expo-image-picker and scanner plugin.
- Confirm scanner plugin config is necessary; if plugin is not an Expo config plugin, move permission strings to ios.infoPlist only.

4. DX and CI (Medium)
- Add eas-cli as devDependency; update scripts to bunx eas ...; document EAS flows in README.
- Consider adding a simple preflight check script (device capability, permissions) for scanning features.

5. UX polish (Medium)
- Add cancel for OCR/PDF processing; expose skip OCR option in UI.
- Add page re-order/delete before PDF generation; optional enhancements like contrast/threshold adjustments.

6. Observability (Low)
- Centralize logging levels; add timing metrics for OCR/PDF; optionally report anonymized perf events.

B) Technical details to add
- Web-only dependencies: pdf-lib and tesseract.js should be dev/runtime deps guarded by Platform.OS === 'web'; lazy-load in web builds to keep native size small.
- Progress API: Add callbacks to performOcrOnImages and createDocumentWithOcr; surface to Scan screen to display per-image progress.
- File naming: Continue using getPdfFileName() but allow user rename post-creation.

C) Risk mitigation
- Library incompatibility: Pin versions compatible with Expo 53; add a Renovate/Dependabot config to monitor updates, and test on device before bumping.
- App size: Document ML Kit impact; consider on-demand OCR (toggle default off) for size/perf sensitive users.
- Web performance: Use Web Workers for Tesseract; avoid blocking main thread.

D) Testing and validation approach
- Unit tests (Jest/RNTL):
  - utils/files.ts: generatePdfFromImages guards (throws when module missing, temp file cleanup), getPdfFileName format, createDocumentWithOcr skipOcr path.
  - utils/ocr.ts: normalizeImageUriForOcr cases; concatenateOcrText; performOcrOnImages error handling (mock MlkitOcr).
  - hooks/useDocuments.ts: validatePdfDocument edge cases; add/update/delete flows.
- Integration tests (RNTL):
  - Scan flow happy path (mock scanner + utils to return fixed paths); UI transitions and success alerts.
  - Viewer overlay toggling; editing and saving OCR text updates storage.
- E2E (Detox):
  - Basic scan → generate → view on iOS/Android simulators using fixture images (skipping OCR to speed up).
- Web tests:
  - If adding web fallbacks, verify image selection → web PDF gen → viewer load path in jsdom-like or Playwright.

---

## Refined plan (superseding 0–3 with hardening and parity)

Phase 4: Web parity and overlay correctness
- Implement utils/files.web.ts and utils/ocr.web.ts; wire Platform selects.
- Fix TextOverlay scaling/rotation using per-page metrics from PDFViewer.
- Add progress reporting callbacks; surface in Scan UI with steps: scanning → PDF → OCR.

Phase 5: Permissions, DX, and UX polish
- Clean Android permissions; confirm iOS Info.plist strings; verify plugin config necessity.
- Add eas-cli devDependency; change scripts to bunx eas ...; document build and testing in README.
- Add Skip OCR toggle; add cancel button during processing; optional pre-PDF page reordering UI.

Phase 6: Testing and CI
- Add unit/integration tests outlined above; basic Detox smoke tests.
- Add CI job to run tests on PR; optional EAS simulator build on main.

---

## Actionable next steps

1) Web parity
- Add pdf-lib and tesseract.js (web-only) and implement Platform-specific utils; document size/perf expectations.

2) Overlay fix
- Adjust TextOverlay to compute scale from OCRPage dimensions and PDFViewer page metrics; add regression tests with synthetic metrics.

3) Permissions cleanup
- Remove deprecated Android storage permissions; validate on Android 13/14.

4) Developer experience
- Add eas-cli as devDependency; update build scripts to bunx; add README section for EAS flows.

5) Testing
- Implement unit tests for utils and hooks; add one integration test around Scan and Viewer flows; add a Detox smoke test.

6) Performance knobs
- Add UI toggles for skip OCR and OCR concurrency; persist preference.

This set will bring the implementation into full alignment with the project’s standards, improve reliability across platforms, and provide a sustainable path for future enhancements.

