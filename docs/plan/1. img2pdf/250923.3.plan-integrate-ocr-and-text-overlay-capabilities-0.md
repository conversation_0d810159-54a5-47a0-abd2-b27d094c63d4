I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the complete codebase and found a well-structured React Native app with Expo SDK 53 running on EAS Development Build. The app already has document scanning with `react-native-document-scanner-plugin`, PDF generation with `react-native-pdf-from-image`, and a comprehensive PDF viewer using PDF.js. The document storage uses React Query with AsyncStorage, and the `Document` interface already supports optional metadata fields. The scanning workflow is complete from image capture to PDF creation and storage. The viewer supports zoom, rotation, sharing, and has a robust architecture for handling both PDFs and images.

### Approach

I'll implement OCR functionality by installing `react-native-mlkit-ocr` and integrating it into the existing scanning workflow. The plan involves extending the Document interface to store OCR results, creating OCR processing utilities, updating both scan screens to perform OCR before PDF generation, and enhancing the viewer with text viewing/editing capabilities. I'll add a text overlay feature to the PDF viewer and ensure the document model supports OCR data storage and updates. The implementation will maintain backward compatibility and follow the existing architectural patterns.

### Reasoning

I examined the project structure and found the current `app.json`, `package.json`, and key implementation files. I analyzed the document scanning workflow in both scan screens, the PDF generation utilities in `utils/files.ts`, the document storage hook in `useDocuments.ts`, and the comprehensive PDF viewer implementation. I researched the `react-native-mlkit-ocr` library requirements and found it integrates well with the existing EAS Development Build setup. I mapped out the complete data flow from scanning to storage to viewing to understand all integration points.

## Mermaid Diagram

sequenceDiagram
    participant User as User
    participant Scanner as Document Scanner
    participant OCR as ML Kit OCR
    participant PDFGen as PDF Generator
    participant Storage as Document Storage
    participant Viewer as PDF Viewer
    participant TextUI as Text Editor

    User->>Scanner: Scan document(s)
    Scanner->>Scanner: Auto detect edges & crop
    Scanner->>OCR: Extract text from images
    OCR->>OCR: Process with ML Kit
    OCR->>Scanner: Return OCR results
    Scanner->>PDFGen: Generate PDF from images
    PDFGen->>Scanner: Return PDF file path
    Scanner->>Storage: Save document with OCR data
    Storage->>User: Show success with text extracted
    
    User->>Viewer: Open document
    Viewer->>Viewer: Load PDF and OCR data
    User->>Viewer: Toggle text overlay
    Viewer->>Viewer: Show text bounding boxes
    
    User->>TextUI: View/Edit extracted text
    TextUI->>TextUI: Display editable text
    User->>TextUI: Save edited text
    TextUI->>Storage: Update document with edited text
    Storage->>Viewer: Refresh document data

## Proposed File Changes

### package.json(MODIFY)

Install the `react-native-mlkit-ocr` dependency:

- Add `react-native-mlkit-ocr` to the dependencies section
- Use the latest stable version that's compatible with React Native 0.79.1 and Expo SDK 53
- Ensure the package is added alongside the existing ML and image processing dependencies

The library provides on-device text recognition using Google's ML Kit without requiring Firebase configuration, making it perfect for this use case.

### app.json(MODIFY)

Add the ML Kit OCR plugin configuration to the plugins array:

- Add `react-native-mlkit-ocr` plugin after the existing `react-native-document-scanner-plugin`
- Configure any required permissions for text recognition (if needed)
- Ensure the plugin is positioned correctly in the plugins array to maintain proper initialization order
- Keep all existing plugins (`expo-router`, `expo-document-picker`, `expo-image-picker`, `expo-camera`, `react-native-document-scanner-plugin`, `expo-dev-client`) intact

The plugin configuration will enable native ML Kit text recognition functionality while maintaining compatibility with the existing setup.

### utils\ocr.ts(NEW)

References: 

- utils\files.ts(MODIFY)

Create OCR processing utilities using `react-native-mlkit-ocr`:

- Import `MlkitOcr` from `react-native-mlkit-ocr`
- Define `OCRBlock` interface with text, bounding box coordinates, and confidence level
- Define `OCRPage` interface with full text and array of blocks
- Add `performOcrOnImage` function that takes a single image URI and returns OCR results
- Add `performOcrOnImages` function that processes multiple images and returns an array of OCR pages
- Handle different image URI formats (file://, data:, etc.) similar to how `generatePdfFromImages` handles them in `utils/files.ts`
- Add proper error handling for OCR failures and unsupported image formats
- Add `concatenateOcrText` utility function to combine text from multiple pages
- Include confidence thresholds and filtering for low-quality text recognition
- Add logging for OCR processing times and success rates

The utilities should handle the conversion from various image URI formats to the format required by ML Kit OCR, and provide structured OCR results that can be stored in the document model.

### hooks\useDocuments.ts(MODIFY)

References: 

- utils\ocr.ts(NEW)

Extend the Document interface and add OCR-related functionality:

- Add optional `ocrText?: string` field for concatenated text from all pages
- Add optional `ocrPages?: OCRPage[]` field for detailed per-page OCR results with bounding boxes
- Add optional `ocrProcessed?: boolean` field to track if OCR has been performed
- Add optional `ocrEdited?: boolean` field to track if user has edited the extracted text
- Import `OCRPage` interface from `utils/ocr.ts`
- Update `validatePdfDocument` function to allow the new optional OCR fields without breaking existing validation
- Add `updateDocumentMutation` using `useMutation` to handle partial document updates (for editing OCR text)
- The update mutation should merge the provided fields with existing document data
- Add proper error handling for update operations
- Update the return object to include `updateDocument: updateDocumentMutation.mutateAsync`
- Ensure backward compatibility with existing documents that don't have OCR fields

The updated interface should support storing both the raw OCR results and user-edited text while maintaining compatibility with existing documents.

### utils\files.ts(MODIFY)

References: 

- utils\ocr.ts(NEW)
- hooks\useDocuments.ts(MODIFY)

Add OCR integration helper functions:

- Import OCR utilities from `utils/ocr.ts`
- Add `createDocumentWithOcr` function that combines PDF generation and OCR processing
- The function should take image URIs, perform OCR on them, generate PDF, and return a complete document object with OCR data
- Add proper error handling for cases where OCR fails but PDF generation succeeds (and vice versa)
- Include timing logs for both OCR and PDF generation processes
- Add option to skip OCR processing if needed (for performance or user preference)
- Update existing utility functions to work seamlessly with the new OCR workflow
- Ensure the function handles cleanup of temporary files created during both OCR and PDF processing

The helper function should provide a single entry point for creating documents with both PDF and OCR data, simplifying the integration in scan screens.

### app\(tabs)\scan\index.tsx(MODIFY)

References: 

- utils\files.ts(MODIFY)
- hooks\useDocuments.ts(MODIFY)

Integrate OCR processing into the camera scanning workflow:

- Import `createDocumentWithOcr` from `utils/files.ts`
- Update `handleCameraScanning` function to use the new OCR-enabled document creation
- Replace the current PDF generation and document creation with `createDocumentWithOcr(scannedImages)`
- Update the processing UI to show "Scanning documents and extracting text..." during OCR processing
- Add progress indicators for both PDF generation and OCR processing phases
- Update success messages to indicate that text has been extracted (e.g., "Document scanned with X pages and text extracted")
- Add error handling specific to OCR failures while allowing PDF creation to continue
- Update the photo library workflow (`handleImagePicker`) similarly to include OCR processing
- Add user feedback about OCR processing time and results
- Update the features list to include "Text recognition and extraction" as a new feature

The updated workflow should seamlessly integrate OCR processing without disrupting the existing user experience, providing feedback about both PDF creation and text extraction progress.

### app\(tabs)\scan\camera.tsx(MODIFY)

References: 

- utils\files.ts(MODIFY)
- hooks\useDocuments.ts(MODIFY)

Integrate OCR processing into the camera scan screen:

- Import `createDocumentWithOcr` from `utils/files.ts`
- Update `processScannedImages` function to use the new OCR-enabled document creation
- Replace the current PDF generation and document creation with `createDocumentWithOcr(capturedImages)`
- Update the processing UI in the preview to show "Converting to PDF and extracting text..." during processing
- Add visual feedback for OCR processing progress alongside PDF conversion
- Update success messages to indicate text extraction completion
- Add error handling for OCR failures while allowing PDF creation to proceed
- Update the preview text to mention text extraction when processing is complete
- Ensure the document creation includes all OCR metadata before navigation to the viewer
- Update instruction text to mention text extraction capabilities

The camera scan screen should provide the same OCR integration as the main scan screen, maintaining consistency in the user experience across both scanning methods.

### components\pdf\TextOverlay.tsx(NEW)

References: 

- components\pdf\PDFViewer.tsx(MODIFY)

Create a text overlay component for PDF viewer:

- Create a React component that renders text bounding boxes over the PDF viewer
- Accept props for OCR pages data, current page number, zoom level, and rotation
- Calculate the correct positioning of text blocks based on PDF page coordinates and current zoom/rotation
- Render semi-transparent overlay boxes that highlight detected text regions
- Add toggle functionality to show/hide the text overlay
- Include touch handlers to select text blocks and show the recognized text in a tooltip
- Handle coordinate transformation between OCR coordinates and PDF viewer canvas coordinates
- Add visual styling for different confidence levels of text recognition
- Ensure the overlay scales and rotates correctly with the PDF viewer
- Add accessibility features for screen readers

The component should integrate seamlessly with the existing PDF viewer while providing an intuitive way to visualize and interact with extracted text.

### components\pdf\PDFViewer.tsx(MODIFY)

References: 

- components\pdf\TextOverlay.tsx(NEW)
- utils\ocr.ts(NEW)

Add text overlay support to the PDF viewer:

- Add optional `textOverlayData?: OCRPage[]` prop to `PDFViewerProps`
- Add optional `showTextOverlay?: boolean` prop to control overlay visibility
- Import and integrate the `TextOverlay` component
- Position the text overlay absolutely over the WebView content
- Pass through zoom, rotation, and page metrics to the overlay component
- Add message handling for overlay interactions (text selection, tooltip display)
- Ensure the overlay coordinates correctly with the PDF canvas rendering
- Add proper z-index management to keep overlay above PDF content but below controls
- Handle overlay updates when zoom or rotation changes
- Add performance optimizations to prevent overlay re-rendering on every PDF interaction

The PDF viewer should support optional text overlay functionality while maintaining all existing features and performance characteristics.

### app\(tabs)\documents\viewer.tsx(MODIFY)

References: 

- hooks\useDocuments.ts(MODIFY)
- components\pdf\PDFViewer.tsx(MODIFY)
- utils\ocr.ts(NEW)

Add OCR text viewing and editing functionality to the document viewer:

- Add state for managing text overlay visibility (`showTextOverlay`)
- Add state for text editing mode (`isEditingText`, `editedText`)
- Import `updateDocument` from `useDocuments` hook
- Add "Text Overlay" toggle button in the header controls next to zoom and rotate buttons
- Add "View Text" button that opens a modal or bottom sheet showing the extracted text
- In the text viewing modal, display `currentDocument.ocrText` in a scrollable text area
- Add "Edit" button in the text modal that switches to editing mode with a TextInput
- Add "Save" and "Cancel" buttons for text editing with proper validation
- Implement `handleSaveText` function that calls `updateDocument` to persist edited text
- Pass `textOverlayData` and `showTextOverlay` props to the PDFViewer component
- Add loading states for text operations and proper error handling
- Update the document details section to show OCR status ("Text extracted", "Text edited", etc.)
- Add keyboard shortcuts for text operations (if applicable)
- Ensure text editing works on both mobile and web platforms

The viewer should provide intuitive access to both text overlay visualization and text editing functionality while maintaining the existing PDF viewing experience.