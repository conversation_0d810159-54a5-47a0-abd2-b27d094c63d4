I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current implementation uses basic camera capture with a simple overlay frame, but lacks real document scanning capabilities. The `react-native-document-scanner-plugin` is perfect for this use case as it provides native document scanning with auto edge detection, cropping, and multi-page support. The project already has EAS Development Build configured, which is required for this plugin. The existing camera permissions and plugin structure in `app.json` make integration straightforward. The plugin's API aligns well with the current PDF generation workflow using `react-native-pdf-from-image`.

### Approach

I'll install and configure `react-native-document-scanner-plugin` to replace the basic camera functionality with advanced document scanning capabilities. The plan involves adding the plugin to the Expo configuration, updating both scan screens to use the document scanner, implementing multi-image selection for multi-page PDFs, and enhancing the UI to support the document scanning workflow. The plugin provides auto edge detection, cropping, and rotation out of the box, which will significantly improve the user experience compared to the current basic camera implementation.

### Reasoning

I examined the current scan screens and found they use basic `expo-camera` functionality with simulated document scanning features. I checked the package.json and found that EAS Development Build and PDF generation are already configured. I researched `react-native-document-scanner-plugin` and discovered it provides exactly the features needed: auto edge detection, cropping, rotation, and multi-page scanning. I also reviewed the current app.json to understand the existing plugin configuration and permissions setup.

## Mermaid Diagram

sequenceDiagram
    participant User as User
    participant ScanScreen as Scan Screen
    participant DocScanner as Document Scanner
    participant PDFGen as PDF Generator
    participant Storage as Document Storage

    User->>ScanScreen: Tap "Camera Scanner"
    ScanScreen->>DocScanner: DocumentScanner.scanDocument(options)
    DocScanner->>DocScanner: Open native scanner
    DocScanner->>DocScanner: Auto detect document edges
    User->>DocScanner: Position & capture document
    DocScanner->>DocScanner: Auto crop & enhance
    User->>DocScanner: Add more pages (optional)
    DocScanner->>ScanScreen: Return scanned images array
    ScanScreen->>PDFGen: generatePdfFromImages(scannedImages)
    PDFGen->>PDFGen: Create multi-page PDF
    PDFGen->>ScanScreen: Return PDF file path
    ScanScreen->>Storage: addDocument(pdfDocument)
    Storage->>User: Show success with page count

## Proposed File Changes

### package.json(MODIFY)

Install the `react-native-document-scanner-plugin` dependency:

- Add `react-native-document-scanner-plugin` to the dependencies section
- Use the latest stable version that's compatible with React Native 0.79.1 and Expo SDK 53
- Ensure the package is added alongside the existing camera and image processing dependencies

The plugin will provide native document scanning capabilities including auto edge detection, cropping, rotation, and multi-page scanning support.

### app.json(MODIFY)

Add the document scanner plugin configuration to the plugins array:

- Add `react-native-document-scanner-plugin` with camera permission configuration
- Set the camera permission message to match the existing camera permission style
- Position the plugin after the existing `expo-camera` plugin in the plugins array
- Ensure the camera permission message is consistent with the existing iOS `NSCameraUsageDescription`
- Keep all existing plugins (`expo-router`, `expo-document-picker`, `expo-image-picker`, `expo-camera`, `expo-dev-client`) intact

The plugin configuration will enable native document scanning functionality while maintaining compatibility with the existing camera setup.

### app\(tabs)\scan\camera.tsx(MODIFY)

References: 

- utils\files.ts
- hooks\useDocuments.ts

Replace the basic camera functionality with the document scanner:

- Import `DocumentScanner` from `react-native-document-scanner-plugin`
- Remove the `CameraView`, camera permissions, and manual capture logic
- Replace the camera UI with a document scanning interface
- Update `takePicture` function to use `DocumentScanner.scanDocument()` with options for high quality and file path response
- Handle the scanner result which returns an array of scanned images with auto edge detection and cropping applied
- Update the preview functionality to show the scanned document instead of raw camera capture
- Modify `processPicture` to work with the scanned document file paths
- Update the UI to reflect document scanning workflow ("Scan Document" instead of "Take Picture")
- Add error handling for scanner cancellation and failures
- Remove camera-specific controls (facing toggle) and replace with scanner-specific options
- Update the instruction text to guide users through the document scanning process

The new implementation will provide professional document scanning with automatic edge detection, perspective correction, and image enhancement.

### app\(tabs)\scan\index.tsx(MODIFY)

References: 

- utils\files.ts
- hooks\useDocuments.ts

Update the camera scanning option to use the document scanner:

- Import `DocumentScanner` from `react-native-document-scanner-plugin`
- Modify `handleCameraScanning` to use `DocumentScanner.scanDocument()` instead of navigating to the camera screen
- Configure the scanner with `maxNumDocuments` option for multi-page scanning
- Set `responseType: 'imageFilePath'` and `croppedImageQuality: 100` for best results
- Handle the scanner result which returns multiple scanned images for multi-page PDFs
- Update the processing flow to use all scanned images with the existing `generatePdfFromImages` function from `utils/files.ts`
- Add proper error handling for scanner cancellation and permission issues
- Update the UI text and descriptions to reflect document scanning capabilities
- Modify the processing message to show "Scanning documents..." and "Converting X pages to PDF..."
- Update the success message to indicate the number of pages scanned
- Keep the photo library option unchanged as it provides a different workflow
- Update the features list to reflect the actual implemented capabilities

The camera scanner option will now provide a complete document scanning experience with multi-page support and automatic PDF generation.