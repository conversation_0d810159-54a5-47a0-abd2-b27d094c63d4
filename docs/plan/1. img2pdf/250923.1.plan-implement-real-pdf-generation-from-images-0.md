I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current implementation simulates PDF conversion by storing image URIs directly as document URIs. The `scan/index.tsx` already supports multiple image selection but only processes the first image. The `scan/camera.tsx` captures single images and stores them as base64 or file URIs. The document storage uses React Query with local storage, and the `Document` interface needs to be updated to handle real PDF files. The `react-native-pdf-from-image` library provides a simple API for converting image arrays to multi-page PDFs.

### Approach

I'll replace the simulated PDF conversion with actual PDF generation using `react-native-pdf-from-image`. The plan involves updating both scan screens to use the real PDF library, handling multiple images for multi-page PDFs, and updating the document storage to work with actual PDF files. I'll also add utility functions for PDF generation and update the document model to properly handle PDF files instead of image URIs.

### Reasoning

I analyzed the current codebase and found that both scan screens (`scan/index.tsx` and `scan/camera.tsx`) are currently simulating PDF conversion by storing image URIs as documents. I examined the `useDocuments` hook and `files.ts` utility to understand the current document storage mechanism. I also researched the `react-native-pdf-from-image` library API to understand how to properly implement PDF generation from images.

## Mermaid Diagram

sequenceDiagram
    participant User as User
    participant ScanScreen as Scan Screen
    participant ImagePicker as Image Picker
    participant PDFGen as PDF Generator
    participant Storage as Document Storage
    participant Viewer as PDF Viewer

    User->>ScanScreen: Select "Photo Library" or "Camera"
    ScanScreen->>ImagePicker: Launch image picker/camera
    ImagePicker->>ScanScreen: Return image URI(s)
    ScanScreen->>PDFGen: generatePdfFromImages(imageURIs)
    PDFGen->>PDFGen: Convert images to PDF
    PDFGen->>ScanScreen: Return PDF file path
    ScanScreen->>Storage: addDocument(pdfDocument)
    Storage->>Storage: Save document metadata
    ScanScreen->>User: Show success message
    User->>Viewer: Choose to view document
    Viewer->>Storage: Load PDF from file path

## Proposed File Changes

### utils\files.ts(MODIFY)

Add PDF generation utilities using `react-native-pdf-from-image`:

- Import `createPdf` from `react-native-pdf-from-image`
- Add `generatePdfFromImages` function that takes an array of image URIs and returns a PDF file path
- Handle both single and multiple images for multi-page PDFs
- Use A4 paper size as default with option to customize
- Add proper error handling for PDF generation failures
- Add `getPdfFileName` utility function to generate consistent PDF file names with timestamps
- Update existing file utilities to work with both image URIs and PDF file paths

The function should handle the conversion from image URIs (from camera or photo library) to absolute file paths that the PDF library requires, and return the generated PDF file path for storage.

### app\(tabs)\scan\index.tsx(MODIFY)

References: 

- utils\files.ts(MODIFY)
- hooks\useDocuments.ts(MODIFY)

Replace simulated PDF conversion with real PDF generation:

- Import `generatePdfFromImages` from `utils/files.ts`
- Update `handleImagePicker` function to process all selected images (not just the first one)
- Replace the setTimeout simulation with actual PDF generation using `generatePdfFromImages`
- Handle multiple image selection properly for multi-page PDFs
- Update the document object creation to use the actual PDF file path returned from PDF generation
- Add proper error handling for PDF generation failures with user-friendly error messages
- Update the processing UI to show "Converting images to PDF..." instead of generic processing message
- Ensure the document size reflects the actual PDF file size
- Update success message to indicate the number of pages created when multiple images are selected

The flow should be: select images → generate PDF from all images → create document with PDF file path → show success message with option to view the generated PDF.

### app\(tabs)\scan\camera.tsx(MODIFY)

References: 

- utils\files.ts(MODIFY)
- hooks\useDocuments.ts(MODIFY)

Replace simulated PDF conversion with real PDF generation:

- Import `generatePdfFromImages` from `utils/files.ts`
- Update `processPicture` function to use actual PDF generation instead of storing the image URI directly
- Remove the base64 conversion logic since PDF generation will handle the image processing
- Use `generatePdfFromImages` with the captured image URI to create a single-page PDF
- Update the document object creation to use the actual PDF file path returned from PDF generation
- Add proper error handling for PDF generation failures with platform-specific error messages
- Update processing UI text to show "Converting image to PDF..." instead of generic processing message
- Ensure the document size reflects the actual PDF file size instead of estimated image size
- Remove the platform-specific base64 handling since PDF generation will handle file URIs directly

The flow should be: capture image → generate PDF from single image → create document with PDF file path → navigate to document viewer or return to scan screen.

### hooks\useDocuments.ts(MODIFY)

References: 

- utils\files.ts(MODIFY)

Update document handling to support real PDF files:

- Update the `Document` interface to include optional `pageCount` property for multi-page PDFs
- Add optional `originalImages` property to track source images used for PDF generation
- Update the `addDocumentMutation` to validate that PDF documents have proper file extensions
- Add validation to ensure PDF documents have valid file paths (not base64 or blob URIs)
- Update error handling to provide specific messages for PDF-related validation failures
- Add helper function `validatePdfDocument` to check if a document is a valid PDF with proper file path
- Ensure backward compatibility with existing documents that might still have image URIs

The updated interface should support both legacy image-based documents and new PDF documents, with proper validation to ensure data integrity.