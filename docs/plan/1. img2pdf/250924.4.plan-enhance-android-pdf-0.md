I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

Based on the comprehensive review document analysis, the img2pdf functionality is largely implemented and working on Android. The main gaps are around **text overlay coordinate accuracy**, **Android permissions cleanup**, **developer experience improvements**, and **UX enhancements** for long-running operations. The review specifically identified that web-related issues should be ignored for this implementation, focusing solely on Android hardening and polish.

### Approach

This plan implements the Android-focused enhancement recommendations from the img2pdf-augment-review.md document. We'll tackle the issues in priority order: **1) Fix text overlay coordinate mapping** for accurate OCR text positioning, **2) Clean up Android permissions** by removing deprecated storage permissions, **3) Improve developer experience** with proper EAS CLI setup, **4) Add progress reporting and cancellation** for better UX during scanning operations, and **5) Add OCR skip option** for faster processing when text extraction isn't needed. Each enhancement builds upon the existing solid foundation without breaking current functionality.

### Reasoning

I read the comprehensive img2pdf-augment-review.md document which provided detailed analysis of the current implementation state and specific enhancement recommendations. I then explored the key implementation files including utils/files.ts, utils/ocr.ts, the scan screen, PDF viewer, and text overlay components to understand the current architecture and integration points. The review document clearly identified Android-specific gaps while noting that web-related recommendations should be ignored for this implementation.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant ScanScreen
    participant DocumentScanner
    participant FilesUtil
    participant OCRUtil
    participant PDFViewer
    participant TextOverlay

    User->>ScanScreen: Tap Camera Scanner
    ScanScreen->>ScanScreen: Show OCR Skip Option
    User->>ScanScreen: Configure OCR preference
    ScanScreen->>DocumentScanner: scanDocument()
    DocumentScanner-->>ScanScreen: scannedImages[]
    
    ScanScreen->>FilesUtil: createDocumentWithOcr(images, {onProgress, cancelToken})
    FilesUtil->>FilesUtil: Generate PDF
    FilesUtil->>ScanScreen: Progress: "Generating PDF..."
    
    alt OCR not skipped
        FilesUtil->>OCRUtil: performOcrOnImages(images, {onProgress, cancelToken})
        loop For each image
            OCRUtil->>OCRUtil: Process OCR
            OCRUtil->>FilesUtil: Progress: "Processing page X of Y"
            FilesUtil->>ScanScreen: Progress update
            
            alt User cancels
                ScanScreen->>FilesUtil: Set cancelToken.cancelled = true
                FilesUtil->>OCRUtil: Check cancellation
                OCRUtil-->>FilesUtil: Partial results + cleanup
                FilesUtil-->>ScanScreen: Cancelled with cleanup
            end
        end
        OCRUtil-->>FilesUtil: OCR results
    end
    
    FilesUtil-->>ScanScreen: Complete document
    ScanScreen->>User: Show success with View option
    
    User->>PDFViewer: View document
    PDFViewer->>PDFViewer: Load PDF and send pageMetrics
    PDFViewer->>TextOverlay: Updated pageMetrics
    TextOverlay->>TextOverlay: Transform coordinates with accurate scaling
    TextOverlay->>User: Display properly aligned OCR overlay

## Proposed File Changes

### components/pdf/TextOverlay.tsx(MODIFY)

References: 

- components/pdf/PDFViewer.tsx(MODIFY)
- utils/ocr.ts(MODIFY)

Fix the coordinate transformation logic in the `transformCoordinates` function to properly scale OCR text blocks based on actual page metrics from PDFViewer. The current implementation scales coordinates by the same pageWidth/pageHeight values, resulting in no transformation effect.

Update the coordinate calculation to:
- Calculate proper scale factors: `scaleX = renderedPageWidth / ocrPage.width` and `scaleY = renderedPageHeight / ocrPage.height`
- Handle cases where OCRPage.width/height are 0 (fallback to scale factor of 1)
- Apply proper translation based on page offset from pageMetrics
- Fix rotation transformation to use the correct page center as rotation origin
- Add memoization to prevent unnecessary re-renders when pageMetrics change
- Add debouncing for performance when metrics update frequently

Add proper error handling for edge cases where pageMetrics are unavailable or OCR dimensions are invalid. Ensure the overlay remains functional even with incomplete data.

### components/pdf/PDFViewer.tsx(MODIFY)

References: 

- components/pdf/TextOverlay.tsx(MODIFY)

Enhance the pageMetrics collection and transmission to provide more accurate data for TextOverlay coordinate mapping. The current metrics may not include all necessary information for precise overlay positioning.

Update the `sendMetrics` function to include:
- Natural page dimensions (unscaled width/height from PDF page)
- Rendered page dimensions (actual canvas size after scaling)
- More precise viewport positioning data
- Page rotation state

Add throttling to the scroll-based metrics updates to improve performance. Ensure metrics are sent immediately after page setup and then throttled during user interactions.

Improve the page visibility detection algorithm to more accurately determine the current page when multiple pages are partially visible.

### app.json(MODIFY)

References: 

- utils/files.ts(MODIFY)

Clean up Android permissions by removing deprecated storage permissions that are no longer needed or recommended for modern Android versions.

Remove `WRITE_EXTERNAL_STORAGE` from the android.permissions array as it's deprecated on Android 10+ and the app uses scoped storage through Expo FileSystem APIs which write to app-private directories.

Keep `READ_EXTERNAL_STORAGE` only if required by the document scanner plugin or image picker. Verify if `READ_MEDIA_IMAGES` should be added for Android 13+ compatibility.

Ensure all remaining permissions are actually required by the app's functionality and properly documented in the permission descriptions.

### package.json(MODIFY)

References: 

- eas.json

Add `@expo/eas-cli` as a devDependency to ensure consistent EAS CLI version across development environments and CI/CD pipelines.

Update all build scripts to use `bunx eas` instead of global `eas` command to align with the project's Bun-first approach mentioned in the coding standards.

Change scripts like:
- `"build:dev:android": "bunx eas build --profile development --platform android"`
- `"build:preview:android": "bunx eas build --profile preview --platform android"`
- `"build:prod:android": "bunx eas build --profile production --platform android"`

This ensures reproducible builds and eliminates dependency on globally installed EAS CLI versions.

### utils/files.ts(MODIFY)

References: 

- utils/ocr.ts(MODIFY)
- app/(tabs)/scan/index.tsx(MODIFY)

Add progress reporting and cancellation support to the `createDocumentWithOcr` function to improve user experience during long-running document processing operations.

Extend the function signature to accept optional callbacks:
- `onProgress?: (progress: { step: string; percentage: number; currentItem?: number; totalItems?: number }) => void`
- `cancelToken?: { cancelled: boolean }`

Implement progress reporting for:
- PDF generation step (single progress event as it's synchronous)
- OCR processing step (per-image progress forwarded from `performOcrOnImages`)

Add cancellation checks at appropriate points:
- Before starting OCR processing
- Between image processing batches
- Clean up partial results if cancelled

Forward progress callbacks to the `performOcrOnImages` function in `utils/ocr.ts` to get granular per-image progress updates.

Ensure backward compatibility by making all new parameters optional with sensible defaults.

### utils/ocr.ts(MODIFY)

References: 

- utils/files.ts(MODIFY)

Enhance the `performOcrOnImages` function to support progress reporting and cancellation for better user experience during batch OCR processing.

Add optional parameters to the function:
- `onProgress?: (progress: { completed: number; total: number; currentImage?: string }) => void`
- `cancelToken?: { cancelled: boolean }`

Implement progress reporting:
- Call `onProgress` after each image is processed
- Include current image index, total count, and optional image URI
- Report progress even for failed images to maintain accurate counts

Add cancellation support:
- Check `cancelToken.cancelled` before processing each batch
- Clean up any temporary files if processing is cancelled
- Return partial results with clear indication of cancellation

Ensure the progress reporting integrates well with the existing error handling and concurrency control mechanisms. Maintain backward compatibility by making new parameters optional.

### app/(tabs)/scan/index.tsx(MODIFY)

References: 

- utils/files.ts(MODIFY)
- hooks/useDocuments.ts

Enhance the scan screen UI to provide better user feedback during document processing with progress reporting, cancellation support, and OCR skip option.

Replace the simple `isProcessing` boolean state with a more detailed processing state:
- `processingState: { status: 'idle' | 'scanning' | 'generating-pdf' | 'processing-ocr' | 'complete'; progress: number; canCancel: boolean }`
- Add cancellation token: `cancelTokenRef = useRef({ cancelled: false })`

Add OCR skip option:
- Include a toggle or checkbox in the UI to allow users to skip OCR processing for faster document creation
- Pass `skipOcr` option to `createDocumentWithOcr` based on user preference
- Store user preference in AsyncStorage for future sessions

Update the processing UI to show:
- Current processing step ("Generating PDF...", "Extracting text from page 2 of 5...")
- Progress percentage when available
- Cancel button during long-running operations
- Different success messages based on whether OCR was performed

Implement proper cleanup when operations are cancelled, ensuring temporary files are removed and UI state is reset appropriately.

Update both `handleCameraScanning` and `handleImagePicker` functions to use the new progress and cancellation features.

### __tests__/TextOverlay.coordinate-mapping.test.tsx(NEW)

References: 

- components/pdf/TextOverlay.tsx(MODIFY)
- utils/ocr.ts(MODIFY)

Create comprehensive unit tests for the TextOverlay coordinate transformation logic to ensure accurate positioning of OCR text blocks.

Test scenarios should include:
- Basic coordinate scaling with different zoom levels
- Rotation transformations (0°, 90°, 180°, 270°)
- Page offset calculations with various viewport positions
- Edge cases with missing or invalid OCR dimensions
- Fallback behavior when pageMetrics are unavailable
- Performance of memoized coordinate calculations

Use mock data to simulate:
- OCRPage objects with various width/height values (including 0)
- PageMetrics with different viewport sizes and offsets
- Different zoom and rotation states

Verify that transformed coordinates:
- Are within expected bounds
- Scale proportionally with zoom changes
- Rotate correctly around page center
- Handle edge cases gracefully without crashing

Include regression tests for the specific coordinate mapping issues identified in the review document.

### __tests__/files.progress-cancel.test.ts(NEW)

References: 

- utils/files.ts(MODIFY)
- utils/ocr.ts(MODIFY)

Create unit tests for the enhanced progress reporting and cancellation functionality in the files utility module.

Test the `createDocumentWithOcr` function with:
- Progress callback invocation at correct intervals
- Proper progress percentage calculations
- Cancellation token handling at various stages
- Cleanup behavior when operations are cancelled
- Backward compatibility with existing function calls
- Error handling when progress callbacks throw exceptions

Mock the dependencies:
- `generatePdfFromImages` to simulate PDF generation
- `performOcrOnImages` to simulate OCR processing with progress
- FileSystem operations for cleanup testing

Verify that:
- Progress events are emitted in the correct order
- Cancellation stops processing at appropriate points
- Partial results are handled correctly
- Temporary files are cleaned up in all scenarios
- Function signatures remain backward compatible

### __tests__/ScanScreen.integration.test.tsx(NEW)

References: 

- app/(tabs)/scan/index.tsx(MODIFY)
- utils/files.ts(MODIFY)

Create integration tests for the enhanced scan screen functionality, focusing on the new progress reporting, cancellation, and OCR skip features.

Test scenarios:
- Complete scan workflow with progress updates
- Cancellation during different processing stages
- OCR skip option functionality
- Error handling with proper UI state reset
- User preference persistence for OCR skip setting
- Proper cleanup of temporary files

Mock external dependencies:
- DocumentScanner.scanDocument
- ImagePicker.launchImageLibraryAsync
- createDocumentWithOcr with progress callbacks
- AsyncStorage for preference persistence

Verify UI behavior:
- Progress indicators update correctly
- Cancel button appears and functions properly
- Success/error messages reflect actual processing results
- OCR skip toggle affects processing behavior
- Loading states transition appropriately

Use React Native Testing Library to simulate user interactions and verify component state changes throughout the scanning workflow.

### README.md(MODIFY)

References: 

- package.json(MODIFY)
- app.json(MODIFY)

Update the README to document the new EAS CLI setup and build process changes, ensuring developers understand the Bun-first approach for build commands.

Add a new section covering:
- EAS CLI setup and usage with Bun (`bunx eas`)
- Build commands for different environments (development, preview, production)
- Android-specific build considerations
- Troubleshooting common build issues

Document the enhanced scanning features:
- Progress reporting during document processing
- Cancellation support for long-running operations
- OCR skip option for faster processing
- Text overlay accuracy improvements

Include information about:
- Required Android permissions and their purposes
- Performance considerations for large documents
- Testing procedures for scanning functionality

Ensure the documentation reflects the current state after implementing all the Android-focused enhancements from the review.