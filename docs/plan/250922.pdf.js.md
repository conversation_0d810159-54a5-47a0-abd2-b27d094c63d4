# PDF.js Viewer Replacement Plan

This document tracks the work to replace the current PDF rendering approach with a robust PDF.js-based viewer that works in Expo’s managed workflow (iOS, Android, Web) without custom native modules.

## Goals
- Reliable PDF rendering across platforms inside `WebView` using PDF.js.
- Keep scanned image rendering (existing `isScanned` flow) but unify via a single viewer component API.
- Provide a graceful fallback to native “Open in…” (`expo-sharing`) for very large files or failures.

## Non‑Goals
- OCR, annotations, edits, or true PDF creation/merging.
- Native modules (unless we later choose the optional native path).

## Deliverables
- `components/pdf/PDFViewer.tsx` component with a stable API.
- Bundled `pdf.js` runtime + worker in `assets/pdfjs/`.
- HTML template for rendering PDF pages, with zoom, page navigation, basic toolbar, and RN bridge.
- Fallback “Open in…” action using `expo-sharing`.
- Refactored `app/(tabs)/documents/viewer.tsx` to use the new component.
- Updated docs and notes in `docs/codebase.md`.

## High‑Level Milestones
1) Vendor PDF.js assets and create viewer HTML template. [COMPLETED]
2) Build `PDFViewer` React component (WebView wrapper + bridge). [COMPLETED]
3) Implement file/URI→base64 utilities and integrate with documents store. [COMPLETED]
4) Replace old viewer logic in `app/(tabs)/documents/viewer.tsx`. [COMPLETED]
5) Add `expo-sharing` fallback and size guardrails. [COMPLETED]
6) Testing (manual scenarios) + docs updates. [PENDING]

---

## Detailed Tasks

### 1) Vendor PDF.js and create viewer HTML [COMPLETED]
- Added folder: `assets/pdfjs/` with `pdf.min.js` and `pdf.worker.min.js` (v3.11.174).
- Viewer HTML is generated dynamically in `components/pdf/PDFViewer.tsx` with `<script src={resolvedAssetUri}>` and workerSrc set to the vendored worker URI using `expo-asset` to resolve local URIs at runtime.

Notes on worker:
- Approach A (preferred): Inline worker script text in a `<script id="pdf-worker">` tag; at runtime create a `Blob` and `URL.createObjectURL(blob)` for `workerSrc`.
- Approach B: Reference `pdf.worker.min.js` via a data URL inlined into the HTML (larger HTML string; less flexible).

### 2) PDFViewer React component [COMPLETED]
- Path: `components/pdf/PDFViewer.tsx`.
- Props:
  - `source: { kind: 'pdf' | 'image'; data: string; name?: string }` // `data` is base64 for PDFs (no prefix), or a full data URL/URI for images.
  - `initialZoom?: number` (default 1), `rotation?: number` (degrees), `style?`.
  - `onReady?()`, `onError?(message: string)`, `onMetrics?(metrics)` (e.g., page count).
- Behavior:
  - For `kind='pdf'`: render a `WebView` pointed to the HTML template via `source={{ html: htmlString }}` or `require(viewer.html)` with base HTML, then send base64 payload via `postMessage` to the WebView (`injectedJavaScriptBeforeContentLoaded` to wire listeners). Use `onMessage` to receive events.
  - For `kind='image'`: render either `Image` in RN or reuse a tiny HTML image viewer in `WebView` to align with zoom/rotate behavior.
  - Expose `zoomIn/zoomOut/fitWidth/rotate` by sending messages to the WebView using `webViewRef.current.postMessage(JSON.stringify(...))`.
  - Handle errors with a visible error UI + “Open in…” fallback CTA.

#### 2a) Lazy page rendering [COMPLETED]
- Implemented with `IntersectionObserver` and placeholders per page.
- On load: compute page 1 viewport (scale/rotation) to size placeholders, observe and render pages on visibility.
- On zoom/rotate/fitWidth: rebuild placeholders and observers to re-render at the new scale.

- Security and stability:
  - `originWhitelist: ['*']` (required for data URLs), `allowFileAccess`, `domStorageEnabled`, `javaScriptEnabled`.
  - Sanitize inbound messages; expect JSON with `{ type, payload }`.

### 3) File utilities (base64 handling) [COMPLETED]
- Create `utils/files.ts` (or inline in `useDocuments` if we prefer fewer files):
  - `async function readPdfAsBase64(uri: string): Promise<string>`
    - Web: if data URL `data:application/pdf;base64,XXX` → return `XXX`; if blob URL → fetch+FileReader→data URL → extract base64.
    - Native: `FileSystem.readAsStringAsync(uri, { encoding: Base64 })`.
  - `function normalizeImageUri(input: string): string` → if already data URL, return as-is; else return `input` (native file URI).
  - `function isLargeFile(size: number, thresholdMB = 20)` → boolean guard for fallback.

### 4) Integrate into `app/(tabs)/documents/viewer.tsx` [COMPLETED]
- Replace `getDocumentViewerHTML` and iframe/object codepaths with the new `PDFViewer` component.
- Decide `kind` based on `document.isScanned` (image) vs PDF.
- Use utils to fetch base64 (PDF) or normalized URI (image) before rendering the viewer.
- Maintain existing UI (title, delete, chat entrypoint, fullscreen toggle, zoom/rotate buttons) and connect them to `PDFViewer` via ref + commands.
- Keep the existing debug pane initially, but prefer simpler, structured logs from `onMessage`.

### 5) Add fallback via `expo-sharing` [COMPLETED]
- Install: `expo install expo-sharing`.
- Implement `async function openInExternalApp(doc)`:
  - Ensure we have a real file URI: if only base64 (web/native), write to `FileSystem.cacheDirectory + '/temp.pdf'`.
  - Use `Sharing.isAvailableAsync()` → `Sharing.shareAsync(uri)` else show instructions.
- Hook fallback CTA in viewer’s error UI and a “...” menu.
- Add guardrails: if `size > N MB`, offer “Open in…” directly (still allow trying PDF.js).

### 6) Testing plan (manual)
- Web:
  - Import PDF via Document Picker (blob → data URL conversion path).
  - Load large (10–20MB) PDF and verify performance/scrolling.
  - Validate zoom, rotate, and page count metrics.
- iOS (Expo Go):
  - Import local PDF; validate render and gestures; test fallback open.
- Android (Expo Go):
  - Same as iOS; pay attention to memory/scroll performance.
- Scanned image flow:
  - Capture via camera; view as image; zoom/rotate; ensure no PDF.js path is triggered.
- Failure scenarios:
  - Corrupt base64; ensure error UI shows and “Open in…” works.

### 7) Documentation & Cleanup
- Update `docs/codebase.md` to describe the new viewer stack and fallback path.
- Inline comments only where needed; otherwise keep code self-explanatory.
- Remove (or comment gate) old viewer HTML generation.

---

## File/Code Changes Summary
- New:
  - `assets/pdfjs/pdf.min.js`
  - `assets/pdfjs/pdf.worker.min.js`
  - `assets/pdfjs/viewer.html`
  - `components/pdf/PDFViewer.tsx`
  - `utils/files.ts` (optional)
- Modified:
  - `app/(tabs)/documents/viewer.tsx`
  - `package.json` (add `expo-sharing` if not present)
  - `docs/codebase.md`

## Risks & Mitigations
- Worker path resolution inside WebView → Use inlined worker script as a blob URL.
- Very large PDFs → Early warning + “Open in…” CTA; avoid preloading all pages.
- WebView memory/scroll jank → Lazy page rendering (render visible pages first), throttle zoom redraws.
- Bundle size → Use minified PDF.js; consider tree-shaking a custom build if size becomes an issue.

## Timeline (suggested)
- Day 1: Vendor PDF.js, basic viewer HTML, wire up `PDFViewer` with a static sample PDF.
- Day 2: Integrate utils, connect to real documents, replace old code in `viewer.tsx`.
- Day 3: Add fallback sharing, polish toolbar, manual testing, docs update.

## Rollback Plan
- Keep the current viewer code behind a feature flag (`USE_OLD_VIEWER`) so we can flip back quickly if issues arise during testing.
